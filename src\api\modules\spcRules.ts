import { API_PREFIX } from "@/api/config/servicePort";
import { ReqPage, ResPage } from "@/api/interface";
import http from "@/api";
import { Measurement } from "@/typings/measurement";
import { SpcRule } from "@/typings/spcRules";
//列表
export const getSpcRulesList = (params?: ReqPage) => {
  return http.get<ResPage<SpcRule.Item[]>>(`${API_PREFIX}/spcRules/listAll`, params);
};
//编辑
export const spcRulesEdit = (params?: ReqPage) => {
  return http.post(`${API_PREFIX}/spcRules/edit`, params);
};
