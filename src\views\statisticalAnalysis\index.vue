<template>
  <div class="main-box">
    <SearchParams @search="search" />
    <ListContent ref="listContentRef" />
  </div>
</template>
<script setup lang="ts">
import SearchParams from "./components/SearchParams.vue";
import ListContent from "./components/ListContent.vue";
import { ref } from "vue";
const listContentRef = ref();
const search = (params: any) => {
  listContentRef.value.search(params);
};
</script>
<style scoped lang="scss"></style>
