<template>
  <div>
    <div class="card table-search mb-2">
      <el-form ref="formRef" :show-message="isZh" label-suffix="" :model="form" label-position="left">
        <el-form-item>
          <el-radio-group v-model="form.dateType" @change="typeChange">
            <el-radio v-for="({ label, value }, index) of date_types" :key="index" :label="value">{{ label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item style="width: 400px; display: inline-block" v-if="form.dateType == 1">
          <el-date-picker
            v-model="form.fDate"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item style="width: 200px; display: inline-block; margin-right: 20px" v-if="form.dateType == 2">
          <el-input v-model="form.days" @input="dayChange" style="width: 260px" type="number">
            <template #append>天</template>
            <template #prepend>最近</template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 400px; display: inline-block" v-if="form.dateType == 2">
          <el-date-picker
            disabled
            v-model="form.sDate"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item style="width: 200px; display: inline-block; margin-left: 20px">
          <el-button type="primary" :icon="Search" @click="search"> {{ $t("搜索") }} </el-button>
          <el-button :icon="Delete" @click="reset"> {{ $t("重置") }} </el-button>
        </el-form-item>

        <el-form-item style="width: 400px; display: inline-block; float: right; margin-right: 100px">
          <div class="clear" style="width: 100%">
            <span class="demonstration">X轴点数区间 ：{{ xRang }}</span>
            <el-slider v-model="xRang" range show-stops :max="48" :show-tooltip="true" />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="main-box">
      <!--      <TreeFilter-->
      <!--        label="name"-->
      <!--        value="name"-->
      <!--        id="name"-->
      <!--        children="list"-->
      <!--        :data="parts"-->
      <!--        :default-value="initParam.part"-->
      <!--        @change="changeTreeFilter"-->
      <!--        ref="treeFilterRef"-->
      <!--      />-->
      <div class="card filter search" style="width: 300px; margin-right: 10px">
        <div class="item">
          <span class="label">产品</span>
          <RemoteSearchDict type="spc_part" v-model="initParam.part" @change="search" />
        </div>
        <div class="item">
          <span class="label">制程</span>
          <RemoteSearchDict type="spc_process" v-model="initParam.process" @change="search" />
        </div>
        <div class="item">
          <span class="label">参数</span>
          <RemoteSearchDict type="spc_parameter" v-model="initParam.parameter" @change="search" />
        </div>
      </div>
      <div class="card table-box">
        <!--        <el-button @click="handleView">数据明细</el-button>-->
        <!--        <el-button @click="handleSet">图表设置</el-button>-->
        <!--        <el-button @click="handleRemark">异常点备注</el-button>-->
        <el-row :gutter="20">
          <el-col :span="12" v-for="(item, index) in chartList" :key="index">
            <ChartItem
              :data="{
                ...item,
                part: initParam.part,
                process: initParam.process,
                parameter: initParam.parameter,
                isView: false,
                rightClick: true,
                infoEdit: true
              }"
              :rang="xRang"
            />
          </el-col>
        </el-row>
      </div>
    </div>
    <DataInfoModal ref="dataInfoRef" />
    <ChartSetModal ref="chartSetModalRef" />
    <RemarkModal ref="remarkModalRef" />
  </div>
</template>
<script setup lang="ts">
import TreeFilter from "@/components/TreeFilter/index.vue";
import { nextTick, onMounted, reactive, ref } from "vue";
import { getPart } from "@/api/modules/measurement";

import { date_types } from "@/utils/dicts";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { ArrowDown, ArrowUp, Delete, Search } from "@element-plus/icons-vue";
import DataInfoModal from "./components/DataInfoModal.vue";
import ChartSetModal from "./components/ChartSetModal.vue";
import RemarkModal from "./components/RemarkModal.vue";
import { findPathByKey, transformListToChildren } from "@/utils/util";
import { getChartX } from "@/api/modules/dataReport";
import dayjs from "dayjs";
import ChartItem from "@/views/dataReport/components/ChartItem.vue";
import RemoteSearchDict from "@/views/components/RemoteSearchDict.vue";
const treeFilterRef = ref();

const chartList = ref<any[]>([]);
const parts = ref<any[]>([]);
const dataInfoRef = ref();
const chartSetModalRef = ref();
const remarkModalRef = ref();
const { isZh } = useLanguageCode();
const treeData = ref<any[]>([]);
const initParam = reactive({ part: "", process: "", parameter: "", startDateTime: "", endDateTime: "" });
const rules = ref([]);
const form = ref({ dateType: 1, sDate: [], fDate: [], days: 0, part: "" });
const xRang = ref([0, 48]);

const typeChange = () => {
  if (form.value.dateType == 2 && form.value.sDate.length < 2) {
    form.value.days = 30;
    dayChange();
  }
};

const dayChange = () => {
  form.value.sDate = [
    dayjs(dayjs().subtract(form.value.days, "day")).format("YYYY-MM-DD HH:mm:ss"),
    dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")
  ];
};
const changeTreeFilter = (val: any) => {
  const arr = findPathByKey(parts.value, val, []);
  if (arr.length > 0) {
    initParam.part = arr[0];
  }
  if (arr.length > 1) {
    initParam.process = arr[1];
  }
  if (arr.length > 2) {
    initParam.parameter = arr[2];
  }

  getList();
};
const search = () => {
  getList();
};
const reset = () => {
  if (form.value.dateType == 2) {
    form.value.days = 30;
    dayChange();
  } else {
    form.value.fDate = [];
  }
  search();
};
const getList = async () => {
  const arr = form.value.dateType == 1 ? form.value.fDate : form.value.sDate;
  if (arr.length > 0) {
    initParam.startDateTime = arr[0];
    initParam.endDateTime = arr[1];
  } else {
    initParam.startDateTime = "";
    initParam.endDateTime = "";
  }

  const { data } = await getChartX(initParam);
  chartList.value = data;
};
const getPartList = async () => {
  const res = await getPart();
  const data = transformListToChildren(res.data);
  parts.value = data;
  // parts
};
const handleView = () => {
  dataInfoRef.value.acceptParams({});
};
const handleSet = () => {
  chartSetModalRef.value.acceptParams({});
};

const handleRemark = () => {
  remarkModalRef.value.acceptParams({});
};

// const xRangChange = () => {};

onMounted(() => {
  getPartList();
  getList();
});
</script>
<style scoped lang="scss">
.search {
  .item {
    margin-bottom: 20px;
  }
  .label {
    font-size: 14px;
  }
}
</style>
