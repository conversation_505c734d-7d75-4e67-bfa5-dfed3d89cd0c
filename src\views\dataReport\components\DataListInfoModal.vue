<template>
  <el-dialog v-model="visible" width="900px" :destroy-on-close="true" :title="$t(`${title}`)" append-to-body>
    <el-table class="mt-2" :data="list" border style="width: 100%">
      <el-table-column label="ID" prop="subgroupId" width="60" />
      <el-table-column label="检测时间" prop="measurementTime" width="180" />
      <el-table-column v-for="(item, index1) in subgroupList" :key="index1" :label="String(index1 + 1)">
        <template #default="scope">
          {{ scope.row.subgroupList[index1].measurementValue }}
        </template>
      </el-table-column>
      <el-table-column label="mean" prop="mean" fixed="right" />
      <el-table-column label="range" prop="range" fixed="right" />
    </el-table>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <!--      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ // $t("确定") }}</el-button>-->
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed } from "vue";
import { getChartDetail, getChartDetailEdit } from "@/api/modules/dataReport";

import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { Post } from "@/typings/post";
import { useDict } from "@/hooks/useDict";
import Table from "@/views/components/Table.vue";

interface IState {
  title: string;
  isView: boolean;
  form: Partial<any>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
  list: any[];
}

const rules = {
  postName: [{ required: true, message: "请填写", trigger: "blur" }],
  postCode: [{ required: true, message: "请填写", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();
const info: any = ref({ subgroupList: [] });
const state = reactive<IState>({
  isView: false,
  title: "数据明细",
  form: {},
  list: []
});
const subgroupList = ref<any[]>([]);
const { form, title, isView, list } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  list.value = params.list;
  let subgroupLists: any = [];
  params.list.forEach(item => {
    if (item.subgroupList.length > subgroupLists.length) {
      subgroupLists = item.subgroupList;
    }
  });

  subgroupList.value = subgroupLists;
  // getInfo();
  setVisible(true);
};
// const getInfo = async () => {
//   const res = await getChartDetail(form.value);
//   info.value = res.data;
// };

// 提交数据（新增/编辑）
// const handleSubmit = async () => {
//   // formRef.value!.validate(async valid => {
//   //   if (!valid) return;
//   try {
//     const { part, process, parameter, subgroupId, subgroupList } = info.value;
//     const params = {
//       part,
//       process,
//       parameter,
//       subgroupId,
//       subgroupList: subgroupList.map((item: any) => {
//         return {
//           detailId: item.detailId,
//           measurementValue: item.measurementValue
//         };
//       })
//     };
//     await getChartDetailEdit(params);
//     ElMessage.success({ message: t(`${title.value}成功！`) });
//     // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
//     // useDictStore().setDict(form.value.dictType!, data);
//     // state.getTableList!();
//     setVisible(false);
//   } catch (error) {
//     console.log(error);
//   }
//   // });
// };

defineExpose({
  acceptParams
});
</script>
