import { API_PREFIX } from "@/api/config/servicePort";
import { ReqPage, ResPage } from "@/api/interface";
import http from "@/api";
import { AlertRules } from "@/typings/alertRules";
import { ControlLimits } from "@/typings/controlLimit";
//列表
export const getInspectionItemsList = (params?: ReqPage) => {
  return http.post<ResPage<ControlLimits.Item[]>>(`${API_PREFIX}/inspectionItems/list`, params);
};

export const inspectionItemsEdit = (params?: ControlLimits.Item) => {
  return http.post(`${API_PREFIX}/inspectionItems/save`, params);
};
export const inspectionItemsDel = (params?: any) => {
  return http.post(`${API_PREFIX}/inspectionItems/del`, params);
};

export const inspectionItemsImport = (params?: any) => {
  return http.post(`${API_PREFIX}/inspectionItems/uploadExcelData`, params);
};

export const inspectionItemsExportTemplate = (params?: any) => {
  return http.downloadGet(`${API_PREFIX}/inspectionItems/exportTmpl`, params);
};
