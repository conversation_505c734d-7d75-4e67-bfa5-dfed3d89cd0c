<template>
  <el-dialog v-model="visible" width="900px" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <el-row>
        <el-col :span="18">
          <el-form-item label="预警规则" prop="ruleName">
            <el-input v-model="form.ruleName" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="规则描述" prop="ruleDescription">
            <el-input
              v-model="form.ruleDescription"
              :autosize="{ minRows: 4, maxRows: 8 }"
              type="textarea"
              :placeholder="$t('请填写')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="邮件列表" prop="emailList">
            <el-input
              v-model="form.emailList"
              :autosize="{ minRows: 4, maxRows: 8 }"
              type="textarea"
              :placeholder="$t('请填写')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态">
            <el-switch :active-value="1" :inactive-value="0" v-model="form.status" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed } from "vue";

import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { Post } from "@/typings/post";
import { useDict } from "@/hooks/useDict";
import { AlertRules } from "@/typings/alertRules";
const { common_status } = useDict("common_status");
interface IState {
  title: string;
  isView: boolean;
  form: Partial<AlertRules.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  ruleName: [{ required: true, message: "请填写", trigger: "blur" }],
  ruleDescription: [{ required: true, message: "请填写", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({ ...form.value, postSort: 1 });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
