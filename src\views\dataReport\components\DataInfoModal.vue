<template>
  <el-dialog v-model="visible" width="900px" :destroy-on-close="true" :title="$t(`${title}`)" append-to-body>
    <el-descriptions class="margin-top" title="" :column="2" border>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">产品名称</div>
        </template>
        {{ info.part }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">制程</div>
        </template>
        {{ info.process }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          <div class="cell-item">参数</div>
        </template>
        {{ info.parameter }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">样本量</div>
        </template>
        {{ info.sampleSize }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">操作员</div>
        </template>
        {{ info.employee }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">创建时间</div>
        </template>
        {{ info.measurementTime }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">LSL</div>
        </template>
        {{ info.lsl }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">USL</div>
        </template>
        {{ info.usl }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">LCL</div>
        </template>
        {{ info.lcl }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">UCL</div>
        </template>
        {{ info.ucl }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">ID</div>
        </template>
        {{ info.subgroupId }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">Mean</div>
        </template>
        {{ info.mean }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">备注</div>
        </template>
        {{ info.notes }}
      </el-descriptions-item>
    </el-descriptions>
    <el-table class="mt-2" :data="[{}]" border style="width: 100%">
      <el-table-column label="序号">
        <template #default> 测量值 </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in info.subgroupList" :key="index" :label="String(index + 1)">
        <template #default>
          <span v-if="isView">{{ item.measurementValue }}</span>
          <el-input v-else v-model="item.measurementValue" />
        </template>
      </el-table-column>

      <el-table-column label="mean" fixed="right">
        <template #default>
          {{ info.mean }}
        </template>
      </el-table-column>
      <el-table-column label="range" fixed="right">
        <template #default>
          {{ info.range }}
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed } from "vue";
import { getChartDetail, getChartDetailEdit } from "@/api/modules/dataReport";

import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { Post } from "@/typings/post";
import { useDict } from "@/hooks/useDict";

interface IState {
  title: string;
  isView: boolean;
  form: Partial<any>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  postName: [{ required: true, message: "请填写", trigger: "blur" }],
  postCode: [{ required: true, message: "请填写", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();
const info: any = ref({ subgroupList: [] });
const state = reactive<IState>({
  isView: false,
  title: "数据明细",
  form: {}
});

const { form, title, isView } = toRefs(state);
const tableData = [
  {
    date: "2016-05-03",
    name: "需要",
    address: "No. 189, Grove St, Los Angeles"
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles"
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles"
  },
  {
    date: "2016-05-01",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles"
  }
];

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, { form: params, isView: params.isView });
  getInfo();
  setVisible(true);
};
const getInfo = async () => {
  const res = await getChartDetail(form.value);
  info.value = res.data;
};

// 提交数据（新增/编辑）
const handleSubmit = async () => {
  // formRef.value!.validate(async valid => {
  //   if (!valid) return;
  try {
    const { part, process, parameter, subgroupId, subgroupList } = info.value;
    const params = {
      part,
      process,
      parameter,
      subgroupId,
      subgroupList: subgroupList.map((item: any) => {
        return {
          detailId: item.detailId,
          measurementValue: item.measurementValue
        };
      })
    };
    await getChartDetailEdit(params);
    ElMessage.success({ message: t(`${title.value}成功！`) });
    // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
    // useDictStore().setDict(form.value.dictType!, data);
    // state.getTableList!();
    setVisible(false);
  } catch (error) {
    console.log(error);
  }
  // });
};

defineExpose({
  acceptParams
});
</script>
