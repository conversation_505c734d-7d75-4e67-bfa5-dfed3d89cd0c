import http from "@/api";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";

const extBaseUrl = `${ADMIN_API_PREFIX}/sys_user_ext`;

export const delUserExtInfo = async (ids: number[]) => {
  return http.delete(`${extBaseUrl}`, { ids });
};

export const getRealToken = async (data: { secretKey: string }) => {
  return http.post<string>(`${ADMIN_API_PREFIX}/auth/real_token`, data);
};

export const getToken = async () => {
  return http.get<string>(`${ADMIN_API_PREFIX}/auth/get_token`, {}, { loading: false });
};

export const getRyToken = async (token: string) => {
  return http.post<{ token: string }>(`${API_PREFIX}/hetSSO/checkToken/${token}`);
};

export const logoutRy = async () => {
  return http.post(`${API_PREFIX}/hetSSO/logout`);
};
export const getUserInfo = async (): Promise<any> => {
  return http.get(`${API_PREFIX}/getInfo`);
};
