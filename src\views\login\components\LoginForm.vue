<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
    <el-form-item prop="username">
      <el-input v-model="loginForm.username">
        <template #prefix>
          <el-icon class="mr-1 el-input__icon" :color="themeColor"><UserFilled /></el-icon>
          <span>{{ $t("账号") }}</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input v-model="loginForm.password" type="password" show-password autocomplete="new-password">
        <template #prefix>
          <el-icon class="mr-1 el-input__icon" :color="themeColor">
            <svg
              t="1700548311789"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="5843"
              width="16"
              height="16"
            >
              <path
                d="M893.155556 366.933333h-108.088889v-73.955555C785.066667 119.466667 679.822222 0 517.688889 0c-199.111111 0-278.755556 119.466667-278.755556 292.977778v73.955555H128C82.488889 366.933333 56.888889 398.222222 56.888889 440.888889v517.688889C56.888889 1001.244444 82.488889 1024 128 1024h765.155556c45.511111 0 73.955556-31.288889 73.955555-73.955556v-512c0-42.666667-28.444444-71.111111-73.955555-71.111111zM566.044444 694.044444l5.688889 85.333334c0 31.288889-28.444444 45.511111-59.733333 45.511111s-51.2-17.066667-51.2-45.511111l-5.688889-85.333334c-17.066667-14.222222-28.444444-36.977778-28.444444-62.577777 0-45.511111 39.822222-82.488889 85.333333-82.488889s85.333333 39.822222 85.333333 85.333333c2.844444 25.6-14.222222 45.511111-31.288889 59.733333z m110.933334-327.111111H349.866667V284.444444c0-36.977778 25.6-190.577778 176.355555-190.577777 96.711111 0 153.6 85.333333 153.6 190.577777v82.488889z"
                :fill="themeColor"
                p-id="5844"
              />
            </svg>
          </el-icon>
          <span>{{ $t("密码") }}</span>
        </template>
      </el-input>
    </el-form-item>
  </el-form>
  <div class="flex items-center justify-between w-full">
    <el-checkbox v-model="remember" :label="$t('记住密码')" size="large" />
    <div class="flex items-center">
      <!--      <el-button round link @click="register"> {{ $t("注册") }} </el-button>-->
      <!--      <el-button round link @click="handleForget"> {{ // $t("忘记密码?") }} </el-button>-->
    </div>
  </div>
  <div class="login-btn">
    <el-button :icon="UserFilled" round size="large" type="primary" :loading="loading" @click="login(loginFormRef)">
      {{ $t("登录") }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { HOME_URL, REGISTER_URL } from "@/config";
import { getTimeState, localSet, localGet } from "@/utils";
import { Login } from "@/api/interface";
import { ElNotification } from "element-plus";
import { loginApi } from "@/api/modules/login";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import { UserFilled } from "@element-plus/icons-vue";
import useGlobalStore from "@/stores/modules/global";
import { useAuthStore } from "@/stores/modules/auth";
import { useUrlSearchParams } from "@vueuse/core";
import { getAppDetail } from "@/api/modules/app";
import type { ElForm } from "element-plus";
import { Staff } from "@/typings/staff";
import { isEmpty } from "@/utils/is";
import md5 from "md5";

interface IProps {
  forgetPassword: boolean;
}

interface IEmit {
  (e: "update:forgetPassword", value: boolean): void;
}

const props = defineProps<IProps>();

const emit = defineEmits<IEmit>();

const router = useRouter();

const userStore = useUserStore();

const tabsStore = useTabsStore();

const keepAliveStore = useKeepAliveStore();

const globalStore = useGlobalStore();

const authStore = useAuthStore();

const themeColor = computed(() => globalStore.primary);

type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive({
  name: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }]
});

const loading = ref(false);

const remember = ref(false);
const loginForm = reactive<Login.ReqLoginForm>({
  username: "admin",
  password: "admin123"
});

const handleForget = () => {
  emit("update:forgetPassword", true);
};

// login
const login = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      const { appCode = "", redirect: redirectUrl = "" } = useUrlSearchParams<{
        appCode: string;
        params: string;
        redirect: string;
      }>("hash");
      // 1.执行登录接口
      if (remember.value) {
        localSet("user_account", {
          ...loginForm,
          remember: remember.value
        });
      }
      // md5(loginForm.password)
      const { token } = await loginApi({ ...loginForm, password: loginForm.password });
      userStore.setToken(token);
      // userStore.setUserInfo(user);
      // if (!isEmpty(appCode)) {
      //   const { data } = await getAppDetail({ code: appCode });
      //   if (!isEmpty(data)) {
      //     debugger;
      //     return (window.location.href =
      //       data.url +
      //       "?authKey=" +
      //       encodeURIComponent(access_token) +
      //       "&redirect=" +
      //       encodeURIComponent(decodeURIComponent(redirectUrl)));
      //   }
      //   userStore.setToken("");
      //   userStore.setUserInfo({} as Staff.Item);
      //   return ElNotification({
      //     title: "错误提示",
      //     message: "暂无应用编码 " + appCode + " 的应用",
      //     type: "error",
      //     duration: 3000
      //   });
      // }

      // 2.添加动态路由
      await initDynamicRouter();

      // 3.清空 tabs、keepAlive 数据
      await tabsStore.setTabs([]);
      await keepAliveStore.setKeepAliveName([]);

      // await globalStore.getBaseSetting();

      const flatRoutePath = authStore.flatMenuListGet.map(item => item.path);

      const redirect = flatRoutePath.includes(HOME_URL) ? HOME_URL : (flatRoutePath[0] ?? "/404");
      // 4.跳转到首页
      // console.log("跳转到首页");
      // return;
      // window.location.href = redirect;
      await router.push(redirect);
      ElNotification({
        title: getTimeState(),
        message: loginForm.username + "欢迎登录~",
        type: "success",
        duration: 3000
      });
    } finally {
      loading.value = false;
    }
  });
};

const register = () => {
  router.push({ path: REGISTER_URL });
};

// resetForm
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

onMounted(() => {
  const data = localGet("user_account");

  if (data) {
    loginForm.username = data.username;
    loginForm.password = data.password;
    remember.value = data.remember;
  }

  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value) return;
      login(loginFormRef.value);
    }
  };
});
</script>

<style scoped lang="scss">
@import "../index.scss";
</style>
