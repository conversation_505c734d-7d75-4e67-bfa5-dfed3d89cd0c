import { ReqPage, ResPage } from "@/api/interface/index";
import { Staff, staffForm } from "@/typings/staff";
import http from "@/api";
import { API_PREFIX, ADMIN_API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}`;

export const getStaffList = (params?: Partial<Staff.Item & ReqPage>) => {
  return http.get<ResPage<Staff.Item>>(`${API_PREFIX}/system/user/list`, params);
};

export const getPublicUser = (params?: Partial<Staff.Item & ReqPage>) => {
  return http.get<ResPage<any>>(`${API_PREFIX}/system/user/supplierList`, params);
};

export const getStaffInfo = (userId: string | undefined) => {
  return http.get<any>(`${API_PREFIX}/system/user/` + userId);
};

export const createStaff = (data: Staff.Item) => {
  // createStaffAudit({ ...data, status: "0" });
  return http.post(`${API_PREFIX}/system/user`, data);
};

// export const createStaffPublic = (data: Staff.Item) => {
//   // createStaffAudit({ ...data, status: "0" });
//   return http.post(`${API_PREFIX}_public`, data);
// };
export const userRegister = (data: Staff.Item) => {
  // createStaffAudit({ ...data, status: "0" });
  return http.post(`${API_PREFIX}/register`, data);
};
export const editStaff = (data: Staff.Item) => {
  return http.put(`${API_PREFIX}/system/user`, data);
};

export const deleteStaff = (data: string) => {
  return http.delete(`${API_PREFIX}/system/user/` + data);
};

export const exportStaff = (data: Partial<Staff.Item & { ids?: number[]; isTemplate?: boolean }>) => {
  return http.post(`${API_PREFIX}/system/user/export`, data);
};

// 下载人员导入模板
export const importStaffTemp = () => {
  return http.get(`/static/template/staff.xlsx`, undefined, { responseType: "blob" });
};

// 导入人员
export const importStaff = (formData: FormData) => {
  return http.post(`${baseUrl}/import`, formData);
};

export const auditStaff = (data: { status: "0" | "1" | "2"; rowId: number[]; reason?: string }) => {
  return http.put(`${baseUrl}/audit`, data);
};

export const setStaffRole = (data: { ids: number[]; roleId: number[] }) => {
  return http.put(`${baseUrl}/role`, data);
};

export const editPassword = (data: { password: string; oldPassword: string }) => {
  return http.put<Staff.Item>(`${baseUrl}/password`, data);
};

export const updateStaffLangCode = (data: { code: string }) => {
  return http.put<Staff.Item>(`${baseUrl}/update_lang_code`, data);
};

export const getStaffFilterList = (data: { code: string }) => {
  return http.get<Staff.Item>(`${baseUrl}/update_lang_code`, data);
};
export const getStaffFilterByQualityPersonList = (data: { code: string }) => {
  return http.get<Staff.Item>(`${baseUrl}/update_lang_code`, data);
};

export const updateAvatarFile = data => {
  return http.post(`${API_PREFIX}/system/user/profile/avatar`, data);
};

export const userResetPwd = (data: any) => {
  return http.put(`${API_PREFIX}/system/user/resetPwd`, data);
};
