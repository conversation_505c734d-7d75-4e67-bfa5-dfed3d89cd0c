<template>
  <el-dialog v-model="visible" width="70%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <ProTable :columns="columns" :data="list" :pagination="false" :tool-button="false" ref="proTable" max-height="500">
        <!-- 表格操作 -->
        <template #operation="{ row, $index }">
          <el-button type="primary" link v-if="!row.edit" @click="handleEdit($index)">编辑</el-button>
          <el-button type="primary" link v-if="row.edit" @click="handleSave($index)">保存</el-button>
          <el-button type="danger" link @click="handleDel($index)">删除</el-button>
        </template>
        <template #footerBtn>
          <el-button type="primary" :icon="CirclePlus" @click="handleAddRow">添加行</el-button>
        </template>
      </ProTable>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("关 闭") }}</el-button>
      <!--      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>-->
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="SetControls">
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { ControlLimits } from "@/typings/controlLimit";
import { CirclePlus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { useHandleData } from "@/hooks/useHandleData";
import { isDate, isEmpty } from "@/utils/is";
import { getWarningLimitsList, warningLimitsDelete, warningLimitsEdit } from "@/api/modules/warningLimits";
import { WarningLimits } from "@/typings/warningLimits";
import { useDict } from "@/hooks/useDict";
import { getAlertRulesList } from "@/api/modules/alertRules";
import { getSpcRulesList } from "@/api/modules/spcRules";
const { spc_warning_scope_type } = useDict("spc_warning_scope_type");

interface IState {
  title: string;
  isView: boolean;
  form: Partial<ControlLimits.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const baseItem = {
  edit: true,
  lwl: undefined,
  wl: undefined,
  uwl: undefined,
  effectiveTime: undefined,
  warningRules: [],
  scopeType: []
};

const list = ref<WarningLimits.Item[]>([]);
const rules = {};

const { isZh } = useLanguageCode();

const labelWidth = 0;

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);
const paramsQuery: any = ref({
  pageSize: 10,
  pageNum: 1,
  condition: {
    itemId: 1
  }
});
const arList = ref<any>([]);
// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  Object.assign(state, params);
  paramsQuery.value.condition.itemId = params.form.id;
  await getList();
  await getFormData();
  setVisible(true);
};
const getFormData = async () => {
  const ar = await getAlertRulesList({ pageNum: 1, pageSize: 999 });
  arList.value = ar.data.list;
};
const getList = async () => {
  const { data }: any = await getWarningLimitsList(paramsQuery.value);
  list.value = data.list.map((item: any) => {
    item.scopeTypeStr = item.scopeType;
    item.warningRulesStr = item.warningRules;

    // item.scopeType = item.scopeType.split(",");
    item.warningRules = item.warningRules.split(",");
    return item;
  });
};
// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({ ...form.value });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

const columns = reactive<ColumnProps<WarningLimits.Item>[]>([
  { type: "index", fixed: "left", width: 80, label: "序号" },
  {
    prop: "uwl",
    label: "UWL",
    width: 120,
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-form-item style={{ padding: "15px 0" }} prop={`list[${$index}].label`}>
          <el-input v-model={row.uwl} placeholder="请输入" />
        </el-form-item>
      ) : (
        row.uwl
      );
    }
  },
  {
    prop: "wl",
    label: "WL",
    width: 120,
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-form-item style={{ padding: "15px 0" }} prop={`list[${$index}].label`}>
          <el-input v-model={row.wl} placeholder="请输入" />
        </el-form-item>
      ) : (
        row.wl
      );
    }
  },
  {
    prop: "lwl",
    label: "LWL",
    width: 120,
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-form-item style={{ padding: "15px 0" }} prop={`list[${$index}].label`}>
          <el-input v-model={row.lwl} placeholder="请输入" />
        </el-form-item>
      ) : (
        row.lwl
      );
    }
  },
  {
    prop: "effectiveTime",
    label: "生效时间",
    width: 170,
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-form-item style={{ padding: "15px 0" }} prop={`list[${$index}].label`}>
          <el-date-picker v-model={row.effectiveTime} format="YYYY-MM-DD" value-format="YYYY-MM-DD" type="date" />
        </el-form-item>
      ) : (
        row.effectiveTime
      );
    }
  },
  {
    prop: "scopeType",
    label: "应用范围",
    width: 200,
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-select v-model={row.scopeType} placeholder="请选择">
          {spc_warning_scope_type.value.map(item => (
            <el-option key={item.value} label={item.label} value={item.value} />
          ))}
        </el-select>
      ) : (
        row.scopeTypeStr
      );
    }
  },
  {
    prop: "warningRules",
    label: "预警规则",
    width: 200,
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-select v-model={row.warningRules} placeholder="请选择" multiple>
          {arList.value.map(item => (
            <el-option key={item.id} label={item.ruleName} value={item.ruleName} />
          ))}
        </el-select>
      ) : (
        row.warningRulesStr
      );
    }
  },
  {
    prop: "updateBy",
    label: "修改人"
  },
  {
    prop: "updateTime",
    label: "修改时间",
    width: 180
  },
  { prop: "operation", label: "操作", width: 120, fixed: "right" }
]);

const handleDel = async (index: number) => {
  // const batchDelete = async (id: number | number[]) => {
  //   const ids = isArray(id) ? id : [id];
  await useHandleData(warningLimitsDelete, { ctrlId: list.value[index].ctrlId }, "删除所选信息");

  list.value = list.value!.filter((v, i) => i !== index);
  // proTable.value?.clearSelection();
  // proTable.value?.getTableList();
  // };
};

const handleEdit = (index: number) => {
  if (list.value.find(item => item.edit)) {
    ElMessage.warning({ message: "请保存后再操作" });
  }

  list.value[index]!.edit = true;
};
const handleSave = async (index: number) => {
  const params = { ...list.value[index] };

  if (isEmpty(params.itemId)) {
    params.itemId = paramsQuery.value.condition.itemId;
  }

  params.warningRules = params.warningRules?.join(",");
  // if (!isEmpty(params.effectiveDate)) {
  // params.effectiveDate = dayjs(params.effectiveDate, "YYYY-MM-DD");
  // }
  await warningLimitsEdit(params);
  list.value[index]!.edit = false;
  await getList();
};
const handleAddRow = () => {
  if (list.value.find(item => item.edit)) {
    ElMessage.warning({ message: "请保存后再操作" });
    return;
  }
  const row = JSON.parse(JSON.stringify(baseItem));
  list.value.push(row);
};
defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.top-item {
  display: block;
  width: 100%;
  ::v-deep(.el-form-item__label) {
    display: block;
  }
  ::v-deep(.el-form-item__content) {
    display: block;
  }
}

.btn {
  margin-right: 20px;
  ::v-deep(span) {
    color: var(--el-color-white) !important;
  }
}

::v-deep(.el-form-item) {
  margin-bottom: 0 !important;
  padding: 0 !important;
}
</style>
