import { API_PREFIX } from "@/api/config/servicePort";
import { ReqPage, ResPage } from "@/api/interface";
import http from "@/api";
import { AlertRules } from "@/typings/alertRules";
import { ControlLimits } from "@/typings/controlLimit";
//列表
export const getControlLimitsList = (params?: ReqPage) => {
  return http.post<ResPage<ControlLimits.Item[]>>(`${API_PREFIX}/controlLimits/list`, params);
};

export const controlLimitsEdit = (params?: ControlLimits.Item) => {
  return http.post(`${API_PREFIX}/controlLimits/save`, params);
};
export const controlLimitsDelete = (params?: any) => {
  return http.get(`${API_PREFIX}/controlLimits/delete`, params);
};
