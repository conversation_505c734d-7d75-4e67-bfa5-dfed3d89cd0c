import { isEmpty } from "@/utils/is";

export const formatParams = (p1: any, p2: any, date?: any[]) => {
  delete p1.pageNum;
  delete p1.pageSize;
  const condition = {
    ...p1
  };

  if (!isEmpty(date) && date!.length > 0) {
    condition.startDate = date![0];
    condition.endDate = date![1];
  } else {
    condition.startDate = "";
    condition.endDate = "";
  }

  if (isEmpty(date)) {
    delete condition.startDate;
    delete condition.endDate;
  }

  const result = {
    pageNum: p2.pageNum,
    pageSize: p2.pageSize,
    condition
  };

  delete result.condition.condition;

  return result;
};
//生成规则描述
export function generateRuleDescription(paramsConfig: string, ruleDescription: string) {
  try {
    const params = JSON.parse(paramsConfig);
    return ruleDescription.replace(/{([^{}]+)}/g, (match, key) => (params[key] !== undefined ? params[key] : match));
  } catch (error) {
    console.error("参数解析错误:", error);
    return ruleDescription;
  }
}
//统计规则提取
export function parseRuleToStructure(ruleDescription: string | undefined, paramsConfig: string | undefined) {
  try {
    // 解析参数配置
    const params = JSON.parse(paramsConfig);

    const segments = [];
    const regex = /{([^{}]+)}/g;
    let lastIndex = 0;

    let match;
    while ((match = regex.exec(ruleDescription)) !== null) {
      // 添加文本部分
      if (match.index > lastIndex) {
        segments.push({
          text: ruleDescription?.substring(lastIndex, match.index),
          type: "text"
        });
      }

      // 添加变量部分
      const paramKey = match[1];
      segments.push({
        text: paramKey,
        value: Number(params[paramKey] !== undefined ? String(params[paramKey]) : match[0]),
        type: "value"
      });

      lastIndex = regex.lastIndex;
    }

    // 添加剩余文本部分
    if (lastIndex < ruleDescription.length) {
      segments.push({
        text: ruleDescription.substring(lastIndex),
        type: "text"
      });
    }

    return segments;
  } catch (error) {
    console.error("解析规则结构时出错:", error);
    return [];
  }
}
export function transformListToChildren(arr: any) {
  return arr.map(item => {
    const newItem = { ...item };

    if (Array.isArray(item.list)) {
      newItem.children = item.list.map(child => {
        if (typeof child === "string" || typeof child === "number") {
          return { name: String(child) };
        } else if (typeof child === "object") {
          return transformListToChildren([child])[0];
        }
        return child;
      });
      delete newItem.list;
    }

    return newItem;
  });
}
export function findPathByKey(tree: any[], targetName: string, path: any = []) {
  for (const node of tree) {
    const currentPath = [...path, node.name];
    if (node.name === targetName) {
      return currentPath;
    }
    if (node.children) {
      const foundPath: any = findPathByKey(node.children, targetName, currentPath);
      if (foundPath) return foundPath;
    }
  }
  return null;
}
export class chartTypes {}
