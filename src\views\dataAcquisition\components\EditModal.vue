<template>
  <el-dialog v-model="visible" width="900px" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-descriptions class="margin-top" title="" :column="2" border>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">产品名称</div>
        </template>
        {{ form.part }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">制程</div>
        </template>
        {{ form.process }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          <div class="cell-item">参数</div>
        </template>
        {{ form.parameter }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">样本量</div>
        </template>
        {{ form.sampleSize }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">序号</div>
        </template>
        {{ form.sampleNo }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">操作员</div>
        </template>
        {{ form.employee }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">下公差</div>
        </template>
        {{ form.usl }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">上公差</div>
        </template>
        {{ form.lsl }}
      </el-descriptions-item>
    </el-descriptions>
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      style="margin-top: 30px"
    >
      <el-row>
        <el-col :span="18">
          <el-form-item label="测量值" prop="measurementValue">
            <el-input-number style="width: 100%" v-model="form.measurementValue" controls-position="right" />
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="备注" prop="postName">
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 8 }"
              type="textarea"
              :placeholder="$t('请填写')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed } from "vue";

import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { Post } from "@/typings/post";
import { useDict } from "@/hooks/useDict";
import { Measurement } from "@/typings/measurement";
const { common_status } = useDict("common_status");
interface IState {
  title: string;
  isView: boolean;
  form: Partial<Measurement.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  measurementValue: [{ required: true, message: "请填写", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};

// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({ ...form.value, postSort: 1 });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
