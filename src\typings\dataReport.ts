export interface chartVo {
  chartDataList: ChartDataList[];
  /**
   * 控制图类型(X:均值图，R:极差图)
   */
  chartType: string;
  /**
   * 违规规则
   */
  rule: Rule;
  [property: string]: any;
}

export interface ChartDataList {
  cl?: number;
  data?: number;
  lsl?: number;
  /**
   * 测量ID
   */
  measurementId?: number;
  /**
   * 是否违规
   */
  outOfControl?: boolean;
  /**
   * 子组ID
   */
  subgroupId?: number;
  usl?: number;
  [property: string]: any;
}

/**
 * 违规规则
 */
export interface Rule {
  T: number;
  [property: string]: any;
}

export interface AbnormalForm {
  abnormalId: number;
  measurementId: number;
  chartType: string;
  notes: string;
  requiresCorrection: number; //是否触发整改(0:否，1:是)
  rootCauseAnalysis: string;
  correctionAction: string;
}
