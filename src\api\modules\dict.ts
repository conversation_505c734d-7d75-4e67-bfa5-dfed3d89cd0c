import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Dict } from "@/typings/dict";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/sysDictType`;
const dictDataBaseUrl = `${API_PREFIX}/sysDictData`;

export const getDictTypeList = (params: ReqPage) => {
  return http.get<ResPage<Dict.ITypeItem>>(`${API_PREFIX}/system/dict/type/list`, params);
};

export const getDictDataList = (type: string, params: any) => {
  return http.get<Dict.Detail>(`${API_PREFIX}/system/dict/data/type/${type}`, params);
};
export const getDictDataLists = (params: ReqPage) => {
  return http.get<ResPage<Dict.IDataItem>>(`${API_PREFIX}/system/dict/data/list`, params);
};
export const delDictType = (data: string) => {
  return http.delete(`${API_PREFIX}/system/dict/type/` + data);
};

export const createDict = (data: Dict.Detail) => {
  return http.post(`${API_PREFIX}/system/dict/type`, data);
};

export const editDict = (data: Dict.Detail) => {
  return http.put(`${API_PREFIX}/system/dict/type`, data);
};
export const createDataDict = (data: Dict.Detail) => {
  return http.post(`${API_PREFIX}/system/dict/data`, data);
};

export const editDataDict = (data: Dict.Detail) => {
  return http.put(`${API_PREFIX}/system/dict/data`, data);
};
export const editDictData = (data: Dict.Detail) => {
  return http.post(`${API_PREFIX}/batchSaveDictData`, data);
};

export const delDictData = (data: string) => {
  return http.delete(`${API_PREFIX}/system/dict/data/` + data);
};

export const getImportTemplate = () => {
  return http.post(`${API_PREFIX}/exportTmpl`, {});
};
export const getAdminDictDataList = (params: Partial<{ type: string; status: string }>) => {
  return http.get<Dict.IDataItem[]>(`${API_PREFIX}/sys_dict/data/list`, params);
};
