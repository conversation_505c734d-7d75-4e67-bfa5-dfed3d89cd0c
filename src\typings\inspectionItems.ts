export namespace InspectionItems {
  export interface Item {
    id: string;
    part: string;
    process: string;
    parameter: string;
    dataType: string;
    attributes: string;
    formulaVariables: string;
    calculationFormula: string;
    usl: number;
    lsl: number;
    chartType: string;
    subgroupSize: number;
    validationRules: string;
    warningRules: string;
    displayInDashboard: number;
  }
}
