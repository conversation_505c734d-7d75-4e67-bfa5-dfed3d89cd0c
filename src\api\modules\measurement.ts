import { API_PREFIX } from "@/api/config/servicePort";
import { ReqPage, ResPage } from "@/api/interface";
import http from "@/api";
import { Language } from "@/typings/language";
import { Measurement } from "@/typings/measurement";

//列表
export const getMeasurementList = (params?: ReqPage) => {
  return http.post<ResPage<Measurement.Item>>(`${API_PREFIX}/measurement/list`, params);
};

//编辑
export const measurementEdit = (params?: Measurement.Item) => {
  return http.post<ResPage<Measurement.Item>>(`${API_PREFIX}/measurement/edit`, params);
};
//删除
export const measurementDel = (params?: any) => {
  return http.post<ResPage<Measurement.Item>>(`${API_PREFIX}/measurement/del`, params);
};
//导出
export const getMeasurementExport = (params?: ReqPage) => {
  return http.post(`${API_PREFIX}/measurement/export`, params);
};
//导入
export const getMeasurementImport = (params?: FormData) => {
  return http.post(`${API_PREFIX}/measurement/uploadExcelData`, params);
};
//模板
export const getMeasurementExportTmpl = () => {
  return http.downloadGet(`${API_PREFIX}/measurement/exportTmpl`);
};

//统计列表
export const getMeasurementOverallList = (params?: ReqPage) => {
  return http.post<ResPage<Measurement.Item>>(`${API_PREFIX}/measurement/overallList`, params);
};
//统计列表
export const measurementOverallExport = (params?: ReqPage) => {
  return http.post<ResPage<Measurement.Item>>(`${API_PREFIX}/measurement/overallExport`, params);
};
//统计列表 canpin
export const getPart = (params?: ReqPage) => {
  return http.get(`${API_PREFIX}/measurement/part`, params);
};
