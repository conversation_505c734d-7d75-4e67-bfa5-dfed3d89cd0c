<template>
  <el-dialog v-model="visible" width="700px" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      style="margin-top: 30px"
    >
      <el-form-item label="备注">
        <el-input
          :autosize="{ minRows: 4, maxRows: 8 }"
          type="textarea"
          v-model="form.notes"
          :placeholder="$t('请填写')"
          clearable
        />
      </el-form-item>
      <el-form-item label="是否触发整改">
        <el-radio-group v-model="form.requiresCorrection">
          <el-radio :value="1">是</el-radio>
          <el-radio :value="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="原因分析" v-if="form.requiresCorrection === 1">
        <el-input
          :autosize="{ minRows: 4, maxRows: 8 }"
          type="textarea"
          v-model="form.rootCauseAnalysis"
          :placeholder="$t('请填写')"
          clearable
        />
      </el-form-item>
      <el-form-item label="改善措施" v-if="form.requiresCorrection === 1">
        <el-input
          :autosize="{ minRows: 4, maxRows: 8 }"
          type="textarea"
          v-model="form.correctionAction"
          :placeholder="$t('请填写')"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { AbnormalForm } from "@/typings/dataReport";
import { abnormalEdit, abnormalInfo } from "@/api/modules/dataReport";
interface IState {
  title: string;
  isView: boolean;
  form: Partial<AbnormalForm>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  postName: [{ required: true, message: "请填写", trigger: "blur" }],
  postCode: [{ required: true, message: "请填写", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "异常点备注",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, { form: params });
  getInfo();
  setVisible(true);
};

const getInfo = async () => {
  const { data } = await abnormalInfo(form.value);
  Object.assign(form.value, { requiresCorrection: 1 }, data);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await abnormalEdit({ ...form.value });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      // state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.color-item {
  border: 1px #d8d8d8 solid;
  border-radius: 5px;
  padding: 3px;
  width: 200px;
  ::v-deep(.el-color-picker) {
    float: right;
  }
  span {
    margin-left: 20px;
  }
}
.title {
  font-weight: bold;
  font-size: 16px;
  margin: 20px 0;
}
</style>
