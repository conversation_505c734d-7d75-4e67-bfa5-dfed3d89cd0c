<template>
  <el-drawer size="500" v-model="visible" append-to-body>
    <template #header="{ titleId, titleClass }">
      <h4 :id="titleId" :class="titleClass">{{ title }}</h4>
      <el-button class="btn" v-show="!isView" type="primary" @click="handleSubmit">保存</el-button>
    </template>
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <el-row>
        <el-col :span="20">
          <el-col :span="24">
            <el-form-item label="产品名称" prop="part">
              <!--              <el-input v-model="form.part" />-->
              <RemoteSearchDict type="spc_part" v-model="form.part" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="制程" prop="process">
              <!--              <el-select v-model="form.process">-->
              <!--                <el-option v-for="{ label, value } of spc_process" :key="value" :label="label" :value="value" />-->
              <!--              </el-select>-->
              <!--              <el-input v-model="form.process" />-->
              <RemoteSearchDict type="spc_process" v-model="form.process" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="参数" prop="parameter">
              <!--              <el-input v-model="form.parameter" />-->
              <RemoteSearchDict type="spc_parameter" v-model="form.parameter" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="数据类型" prop="dataType">
              <el-radio-group v-model="form.dataType">
                <el-radio v-for="({ label, value }, index) of data_type" :key="index" :label="value">{{ label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="关联参数" prop="formulaVariables">
              <el-select v-model="form.formulaVariables" multiple>
                <el-option v-for="{ label, value } of spc_parameter" :key="value" :label="label" :value="value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="计算公式" prop="postName">
              <el-input
                v-model="form.calculationFormula"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 8 }"
                placeholder="请填写"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="规格限" prop="postName">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-form-item class="top-item" label="LSL" prop="postName">
                    <el-input-number style="width: 100%" v-model="form.lsl" controls-position="right" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item class="top-item" label="USL" prop="postName">
                    <el-input-number style="width: 100%" v-model="form.usl" controls-position="right" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="控制图" prop="chartType">
              <el-select v-model="form.chartType" multiple>
                <el-option v-for="{ label, value } of spc_chart_type" :key="value" :label="label" :value="value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="子组大小" prop="subgroupSize">
              <el-input-number style="width: 100%" v-model="form.subgroupSize" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="判异规则" prop="postName">
              <el-select v-model="form.validationRules" multiple>
                <el-option v-for="{ ruleCode, id } of srList" :key="id" :label="ruleCode" :value="ruleCode" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警规则" prop="warningRules">
              <el-select v-model="form.warningRules" multiple>
                <el-option v-for="{ ruleName, id } of arList" :key="id" :label="ruleName" :value="ruleName" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-form-item label="首页展示" prop="displayInDashboard">
            <el-switch :active-value="1" :inactive-value="0" v-model="form.displayInDashboard" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-drawer>
</template>

<script setup lang="tsx">
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { useDict } from "@/hooks/useDict";
import { InspectionItems } from "@/typings/inspectionItems";
import { getAlertRulesList } from "@/api/modules/alertRules";
import { getSpcRulesList } from "@/api/modules/spcRules";
import { AlertRules } from "@/typings/alertRules";
import { SpcRule } from "@/typings/spcRules";
import { isArray, isEmpty } from "@/utils/is";
import RemoteSearchDict from "@/views/components/RemoteSearchDict.vue";

const { data_type, spc_parameter, spc_chart_type } = useDict(
  // "spc_process",
  "data_type",
  "spc_parameter",
  "spc_chart_type"
);
interface IState {
  title: string;
  isView: boolean;
  form: Partial<InspectionItems.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const arList = ref<any>([]);
const srList = ref<any>([]);
const rules = {};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "90px" : "90px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};

const getFormData = async () => {
  const ar = await getAlertRulesList({ pageNum: 1, pageSize: 999 });

  arList.value = ar.data.list;
  const sr = await getSpcRulesList({ pageNum: 1, pageSize: 999 });

  srList.value = sr.data;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  const f = JSON.parse(JSON.stringify(params.form));
  if (!isEmpty(f.chartType)) f.chartType = f.chartType.split(",");
  if (!isEmpty(f.formulaVariables)) f.formulaVariables = f.formulaVariables.split(",");
  if (!isEmpty(f.validationRules)) f.validationRules = f.validationRules.split(",");
  if (!isEmpty(f.warningRules)) f.warningRules = f.warningRules.split(",");
  params.form = f;
  Object.assign(state, params);
  setVisible(true);

  getFormData();
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      //chartType formulaVariables validationRules warningRules
      const f = JSON.parse(JSON.stringify(form.value));
      if (isArray(f.chartType)) f.chartType = f.chartType.join(",");
      if (isArray(f.formulaVariables)) f.formulaVariables = f.formulaVariables.join(",");
      if (isArray(f.validationRules)) f.validationRules = f.validationRules.join(",");
      if (isArray(f.warningRules)) f.warningRules = f.warningRules.join(",");

      await state.api!({ ...f });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.top-item {
  display: block;
  width: 100%;
  ::v-deep(.el-form-item__label) {
    display: block;
  }
  ::v-deep(.el-form-item__content) {
    display: block;
  }
}

.btn {
  margin-right: 20px;
  ::v-deep(span) {
    color: var(--el-color-white) !important;
  }
}
</style>
