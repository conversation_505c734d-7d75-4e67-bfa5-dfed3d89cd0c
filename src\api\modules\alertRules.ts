import { API_PREFIX } from "@/api/config/servicePort";
import { ReqPage, ResPage } from "@/api/interface";
import http from "@/api";
import { Measurement } from "@/typings/measurement";
import { AlertRules } from "@/typings/alertRules";
//列表
export const getAlertRulesList = (params?: ReqPage) => {
  return http.post<ResPage<AlertRules.Item[]>>(`${API_PREFIX}/alertRules/list`, params);
};

export const alertRulesEdit = (params?: AlertRules.Item) => {
  return http.post(`${API_PREFIX}/alertRules/save`, params);
};
export const alertRulesDel = (params?: any) => {
  return http.post(`${API_PREFIX}/alertRules/del`, params);
};
