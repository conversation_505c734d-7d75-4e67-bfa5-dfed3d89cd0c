<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :data-callback="dataCallback"
      :pagination="null"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
    >
      <!-- 表格 header 按钮 -->
      <!--      <template #tableHeader>-->
      <!--      </template>-->
      <template #operation="{ row }">
        <el-button type="primary" link @click="handleEdit('编辑', row)">编辑</el-button>
      </template>
    </ProTable>
    <EditModal ref="editModalRef" />
  </div>
</template>

<script setup lang="tsx" name="DictTable">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { ReqPage } from "@/api/interface";
import EditModal from "./components/EditModal.vue";

import { useDict } from "@/hooks/useDict";
import { isEmpty } from "@/utils/is";
import { ref, reactive, toRefs, nextTick } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { Post } from "@/typings/post";
import { getSpcRulesList, spcRulesEdit } from "@/api/modules/spcRules";
import { SpcRule } from "@/typings/spcRules";
import { generateRuleDescription } from "@/utils/util";
import { AlertRules } from "@/typings/alertRules";
import { alertRulesEdit } from "@/api/modules/alertRules";
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check } = useCheckSelectId();
const loading = ref(true);
const editModalRef = ref<InstanceType<typeof EditModal> | null>(null);

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<SpcRule.Item & ReqPage>({ pageNum: 1, pageSize: 10 } as SpcRule.Item & ReqPage);

const { common_status } = useDict("common_status");

// 表格配置项
const columns = reactive<ColumnProps<SpcRule.Item>[]>([
  { prop: "ruleCode", label: "规则编号" },
  { prop: "desc", label: "规则描述" },
  {
    prop: "active",
    label: "状态",
    render: ({ row }) => {
      return <el-switch active-value={1} inactive-value={0} v-model={row.active} before-change={() => statusChange(row)} />;
    }
  },
  {
    prop: "operation",
    label: "操作",
    width: 120,
    fixed: "right"
  }
]);

const dataCallback = (data: SpcRule.Item[]) => {
  nextTick(() => (loading.value = false));
  return data?.map(item => {
    item.desc = generateRuleDescription(item.paramsConfig, item.ruleDescription);
    return item;
  });
};

const getTableList = (params: any) => {
  loading.value = true;
  return getSpcRulesList({ ...params });
};

const handleEdit = (title: string, row: Partial<SpcRule.Item> = {}) => {
  const form = JSON.parse(JSON.stringify(row));
  const params = {
    title: title,
    isView: title === "查看",
    form,
    common_status: common_status.value,
    api: spcRulesEdit,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.id)) Object.assign(params.form, { active: 1 });
  editModalRef.value?.acceptParams(params);
};

const statusChange = (row: AlertRules.Item) => {
  if (loading.value) return false;
  return new Promise(async resolve => {
    await spcRulesEdit({ ...row, active: row.active == 1 ? 0 : 1 }).then(res => {
      if (res.code === 200) resolve(true);
      proTable.value?.getTableList();
    });
  });
};
</script>
