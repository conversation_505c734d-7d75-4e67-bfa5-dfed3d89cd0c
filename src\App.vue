<template>
  <el-config-provider :locale="locale" :size="assemblySize" :button="buttonConfig">
    <router-view></router-view>
  </el-config-provider>
</template>

<script setup lang="ts">
import { onMounted, reactive, computed, watchEffect, watch } from "vue";
import { useI18n } from "vue-i18n";
import { getBrowserLang } from "@/utils";
import { useTheme } from "@/hooks/useTheme";
import { ElConfigProvider } from "element-plus";
import { useGlobalStore } from "@/stores/modules/global";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import useLanguageStore from "./stores/modules/language";
import { useIntervalFn, useUrlSearchParams } from "@vueuse/core";
import { useRoute, useRouter } from "vue-router";
import { useFingerprint } from "./hooks/useFingerprint";
import { getToken } from "./api/modules/auth";
import useUserStore from "./stores/modules/user";
import { APP_CODE, LOGIN_URL } from "./config";
import { isProd } from "./utils/is";

const route = useRoute();
const router = useRouter();

const globalStore = useGlobalStore();
useFingerprint();
const languageStore = useLanguageStore();
const languageCode = computed(() => languageStore.languageCode ?? getBrowserLang());
// init theme
const { initTheme } = useTheme();
initTheme();

const userStore = useUserStore();

const { pause } = useIntervalFn(
  async () => {
    if (route.fullPath.includes("login") || window.location.href.includes("login") || !userStore.token) return;
    /* your function */
    // try {
    //   const { data: token, success } = await getToken();
    //   if (!success) {
    //     pause();
    //     userStore.setToken("");
    //     router.replace({ path: LOGIN_URL, query: { logout: 1, appCode: APP_CODE } });
    //   }
    // } catch (e) {
    //   pause();
    //   userStore.setToken("");
    //   router.replace({ path: LOGIN_URL, query: { logout: 1, appCode: APP_CODE } });
    // }
  },
  5000,
  {
    immediate: true,
    immediateCallback: true
  }
);

// function setZoomBasedOnScreenWidth() {
//   document.body.style.zoom = 0.8;
// }
// if (isProd()) {
//   window.onload = setZoomBasedOnScreenWidth;
// }

// init language
const i18n = useI18n();
// element language
const locale = computed(() => {
  if (languageStore.languageCode == "zh") return zhCn;
  if (languageStore.languageCode == "en") return en;
  return getBrowserLang() == "zh" ? zhCn : en;
});

// element assemblySize
const assemblySize = computed(() => globalStore.assemblySize);

// element button config
const buttonConfig = reactive({ autoInsertSpace: false });

// const init = async () => {
//   const { language = "" } = useUrlSearchParams<{ language: string }>("hash");
//   const lang = language || languageStore.languageCode || getBrowserLang();
//   i18n.locale.value = lang;
//   languageStore.setLanguageCode(lang);
//   setLocalMessage();
// };

const setLocalMessage = () => {
  const message = Object.fromEntries(languageStore.languageData.map(item => [item.label, item.value]));
  i18n.locale.value = languageCode.value;
  i18n.setLocaleMessage(languageStore.languageCode, message);
};

// 使用一个标志位来防止重复执行
let isInitialized = false;
watchEffect(async () => {
  if (!languageStore.languageData?.length) {
    await languageStore.getLanguageCodeList();
  }
  if (languageStore.languageData?.length && !isInitialized) {
    setLocalMessage();
    isInitialized = true;
  }
});

watch(
  languageCode,
  () => {
    if (isInitialized) {
      setLocalMessage();
    }
  },
  { deep: true }
);
</script>

<style>
.el-aside {
  --el-menu-bg-color: rgb(8 21 41) !important;
  --el-aside-logo-text-color: #ffffff;
  --el-menu-text-color: #ffffff !important;
  --el-menu-hover-text-color: #ffffff !important;
  --el-menu-hover-bg-color: rgb(255 255 255 / 30%);
}
</style>
