<template>
  <div>
    <div class="mb-1">
      <el-radio-group v-model="dateType" @change="getData">
        <el-radio :value="0">当天</el-radio>
        <el-radio :value="1">本周</el-radio>
        <el-radio :value="2">本月</el-radio>
      </el-radio-group>
    </div>
    <el-row :gutter="20" class="card-info">
      <el-col :span="6">
        <div class="item item1">
          <p class="label">录入参数</p>
          <p class="value">{{ info?.recordedParams || 0 }}</p>
          <el-icon class="icon"><Coin /></el-icon>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="item item2">
          <p class="label">控制限内参数</p>
          <p class="value">{{ info?.validControlParams || 0 }}</p>
          <el-icon class="icon"><Tickets /></el-icon>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="item item3">
          <p class="label">超出控制限参数</p>
          <p class="value">{{ info?.exceededControlParams || 0 }}</p>
          <el-icon class="icon"><PieChart /></el-icon>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="item item4">
          <p class="label">超出规格限参数</p>
          <p class="value">{{ info?.exceededSpecParams || 0 }}</p>
          <el-icon class="icon"><Tickets /></el-icon>
        </div>
      </el-col>
    </el-row>
    <div class="card table-box mt-5">
      <el-row :gutter="20">
        <el-col :span="12" v-for="(item, index) in chartList" :key="index">
          <ChartItem :data="{ ...item, isView: true, listInfoView: true }" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { onMounted, ref } from "vue";

import { Coin, PieChart, Tickets } from "@element-plus/icons-vue";
import { getDashList, getPanelGroup } from "@/api/modules/dashboard";
import { Dashboard } from "@/typings/dashboard";
import ChartItem from "@/views/dataReport/components/ChartItem.vue";
import { isArray } from "@/utils/is";
const dateType = ref(0);
const info = ref<Dashboard.groupVo>();
const chartList: any = ref<any[]>([]);
const getDashInfo = async () => {
  const { data }: any = getPanelGroup({ type: dateType.value });
  info.value = data;
};

const getList = async () => {
  let { data }: any = await getDashList({ type: dateType.value });
  // chartList.value = data.map(item=> )
  let list: any[] = [];

  if (!isArray(data)) data = [];

  data.forEach((item: any) => {
    if (isArray(item.list)) {
      item.list.forEach((m: any) => {
        list.push({
          title: item.title,
          ...m,
          details: item.details
        });
      });
    }
  });

  chartList.value = list;
};
const getData = () => {
  getDashInfo();
  getList();
};
onMounted(() => {
  getDashInfo();
  getList();
});
</script>

<style scoped lang="scss">
.card-info {
  .item {
    border-radius: 6.5px;
    padding: 20px;
    position: relative;
    &.item1 {
      background: linear-gradient(111.801deg, rgb(3, 112, 255) 1.96012e-15%, rgb(99, 201, 255) 100%);
    }
    &.item2 {
      background: linear-gradient(rgb(1, 167, 240) 2.84217e-14%, rgb(149, 242, 2) 100%);
    }
    &.item3 {
      background: linear-gradient(111.801deg, rgb(255, 146, 57) 1.96012e-15%, rgb(255, 203, 151) 100%);
    }
    &.item4 {
      background: linear-gradient(111.801deg, rgb(244, 100, 100) 1.96012e-15%, rgb(254, 151, 151) 100%);
    }
    .label {
      color: #ffffff;
      font-size: 14px;
      padding-bottom: 10px;
    }
    .value {
      color: #ffffff;
      font-size: 16px;
      font-weight: bold;
    }
    .icon {
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      font-weight: bold;
      font-size: 40px;
      color: #ffffff;
    }
  }
}
</style>
