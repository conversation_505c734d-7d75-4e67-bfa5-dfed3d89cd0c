<template>
  <el-dropdown trigger="click" @command="changeLanguage">
    <i :class="'iconfont icon-zhongyingwen'" class="toolBar-icon"></i>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="item of languageList" :key="item.id" :command="item" :disabled="language === item.code">
          {{ $t(item.name) }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { computed } from "vue";
import { useGlobalStore } from "@/stores/modules/global";
import { LanguageType } from "@/stores/interface";
import useLanguageStore from "@/stores/modules/language";
import { Language } from "@/typings/language";
import useUserStore from "@/stores/modules/user";
import { updateStaffLangCode } from "@/api/modules/staff";

const i18n = useI18n();
const globalStore = useGlobalStore();
const languageStore = useLanguageStore();
const language = computed(() => languageStore.languageCode);
const languageList = computed(() => languageStore.languageCodeList);

const changeLanguage = (lang: Language.Item) => {
  i18n.locale.value = lang.code;
  languageStore.setLanguageCode(lang.code);
  const message = lang.langData.reduce((prev, curr) => {
    return { ...prev, [curr.label]: curr.value };
  }, {});

  if (useUserStore().token) {
    updateStaffLangCode({ code: lang.code });
  }
  i18n.setLocaleMessage(lang.code, message);
};
</script>
