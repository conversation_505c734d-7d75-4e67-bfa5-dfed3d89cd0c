<template>
  <div class="card">
    <h3 style="margin-bottom: 30px; font-weight: bold">统计条件设置</h3>
    <el-divider />
    <el-form ref="formRef" :show-message="isZh" label-suffix="" :rules="rules" :model="form" label-position="top">
      <h4>子组大小</h4>
      <el-form-item label="">
        <el-radio-group v-model="form.dateType" @change="typeChange">
          <el-radio v-for="({ label, value }, index) of date_types" :key="index" :label="value">{{ label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" v-if="form.dateType == 1">
        <el-date-picker
          v-model="form.fDate"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="" v-if="form.dateType == 2">
        <div style="margin-bottom: 10px">
          <el-input v-model="form.days" @input="dayChange" style="width: 160px" type="number">
            <template #append>天</template>
          </el-input>
        </div>
        <el-date-picker
          disabled
          v-model="form.sDate"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-divider />
      <h4 style="margin-bottom: 20px">筛选条件</h4>
      <el-form-item label="产品名称">
        <!--        <el-select v-model="form.part">-->
        <!--          <el-option v-for="{ label, value } of spc_part" :key="value" :label="label" :value="value" />-->
        <!--        </el-select>-->
        <!--        <el-input v-model="form.part" />-->
        <RemoteSearchDict type="spc_part" v-model="form.part" />
      </el-form-item>
      <el-form-item label="制程">
        <!--        <el-select v-model="form.process">-->
        <!--          <el-option v-for="{ label, value } of spc_process" :key="value" :label="label" :value="value" />-->
        <!--        </el-select>-->
        <!--        <el-input v-model="form.process" />-->
        <RemoteSearchDict type="spc_process" v-model="form.process" />
      </el-form-item>
      <el-form-item label="参数" multiple>
        <!--        <el-select v-model="form.parameter" multiple>-->
        <!--          <el-option v-for="{ label, value } of spc_parameter" :key="value" :label="label" :value="value" />-->
        <!--        </el-select>-->
        <!--        <el-input v-model="form.parameter" />-->
        <RemoteSearchDict type="spc_parameter" v-model="form.parameter" />
      </el-form-item>
      <el-form-item label="统计量">
        <el-select v-model="form.statisticType" multiple>
          <el-option v-for="{ label, value } of spc_statistic_type" :key="value" :label="label" :value="value" />
        </el-select>
      </el-form-item>
    </el-form>
    <div style="text-align: right">
      <el-button type="primary" @click="handleSubmit">查询</el-button>
      <el-button @click="handleReset">重置</el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { ref } from "vue";
import { useDict } from "@/hooks/useDict";
const emits = defineEmits(["search"]);
const { spc_statistic_type } = useDict("spc_statistic_type");
import { date_types } from "@/utils/dicts";
import dayjs from "dayjs";
import { formatDate, useDateFormat } from "@vueuse/core";
import RemoteSearchDict from "@/views/components/RemoteSearchDict.vue";
const { isZh } = useLanguageCode();

const rules = ref([]);
const form = ref<any>({ dateType: 1, sDate: [], fDate: [], part: undefined, process: undefined, parameter: undefined });

const handleSubmit = () => {
  emits("search", form.value);
};

const handleReset = () => {
  form.value = {
    dateType: 1,
    sDate: [],
    fDate: [],
    part: undefined,
    process: undefined,
    parameter: undefined
  };
  emits("search", form.value);
};
const typeChange = () => {
  if (form.value.dateType == 2 && form.value.sDate.length < 2) {
    form.value.days = 30;
    dayChange();
  }
};

const dayChange = () => {
  form.value.sDate = [
    dayjs(dayjs().subtract(form.value.days, "day")).format("YYYY-MM-DD HH:mm:ss"),
    dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")
  ];
};
</script>
<style scoped lang="scss">
.card {
  margin-right: 10px;
  width: 450px;
}
</style>
