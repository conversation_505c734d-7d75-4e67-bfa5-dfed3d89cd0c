import { API_PREFIX } from "@/api/config/servicePort";
import { ReqPage, ResPage } from "@/api/interface";
import http from "@/api";
import { chartVo } from "@/typings/dataReport";
//列表
export const getChartX = (params?: any) => {
  return http.post<ResPage<chartVo[]>>(`${API_PREFIX}/chart/getX`, params);
};

//点明细
export const getChartDetail = (params?: any) => {
  return http.post(`${API_PREFIX}/chart/details`, params);
};
//点明细编辑
export const getChartDetailEdit = (params?: any) => {
  return http.post(`${API_PREFIX}/chart/detailsEdit`, params);
};
export const inspectionItemsDel = (params?: any) => {
  return http.post(`${API_PREFIX}/inspectionItems/del`, params);
};

//异常点新增或编辑
export const abnormalEdit = (params?: any) => {
  return http.post(`${API_PREFIX}/chart/abnormal/save`, params);
};

//异常点新增或编辑
export const abnormalInfo = (params?: any) => {
  return http.post(`${API_PREFIX}/chart/abnormal/find`, params);
};
