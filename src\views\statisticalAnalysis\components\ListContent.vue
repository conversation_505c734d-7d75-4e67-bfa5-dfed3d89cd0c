<template>
  <div class="table-box">
    <ProTable
      :key="tableKey"
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
    >
      <template #tableHeader>
        <el-button v-auth="'exception:export'" type="primary" @click="handleExport">导出</el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="DictTable">
import { getMeasurementOverallList, measurementOverallExport } from "@/api/modules/measurement";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { ReqPage, ResPage } from "@/api/interface";
import { ref, reactive, toRefs, onMounted } from "vue";

import { Post } from "@/typings/post";
import { Measurement } from "@/typings/measurement";
import { downloadFileByName } from "@/utils/download";
import dayjs from "dayjs";
import { isArray } from "@/utils/is";
import { LOGIN_URL } from "@/config";

// ProTable 实例
const proTable = ref<ProTableInstance>();
const tableKey = ref(0);
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<Measurement.Item & ReqPage>({ pageNum: 1, pageSize: 10, condition: {} } as Measurement.Item & ReqPage);
const subs = [];
// 表格配置项
const _columns = reactive<ColumnProps<Measurement.Item>[]>([
  // { prop: "ids", label: "ID" },
  { prop: "part", label: "产品名称" },
  { prop: "process", label: "制程" },
  { prop: "parameter", label: "参数" },
  {
    prop: "remark",
    label: "时间范围",
    render: ({ row }) => {
      return `${row.earliestTime}~${row.latestTime}`;
    }
  },
  { prop: "subgroupCount", label: "子组大小" }
]);
let columns: any = [..._columns];
const dataCallback = (data: ResPage<Measurement.Item>) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  return getMeasurementOverallList(
    initParam
    //   {
    //   pageSize: 10,
    //   pageNum: 1,
    //   condition: {
    //     // startDateTime: "2025-01-17 00:00:00",
    //     // endDateTime: "2026-02-10 23:59:59",
    //     // part: "Cimarron BP 4D Top",
    //     // process: "M8-6",
    //     // parameter: ""
    //   }
    // }
  );
};

/**
 * 导出
 * @param row
 */
const handleExport = async (row: Measurement.Item) => {
  await downloadFileByName(measurementOverallExport, initParam.condition);
};

const search = (params: any) => {
  const { dateType, fDate, sDate, part, process, parameter, statisticType } = params;
  let startDateTime: any = "";
  let endDateTime: any = "";
  if (dateType == 1) {
    if (fDate.length > 0) {
      startDateTime = dayjs(fDate[0]).format("YYYY-MM-DD HH:mm:ss");
      endDateTime = dayjs(fDate[1]).format("YYYY-MM-DD HH:mm:ss");
    }
  }
  if (dateType == 2) {
    if (sDate.length > 0) {
      startDateTime = sDate[0];
      endDateTime = sDate[1];
    }
  }

  initParam.condition = {
    part,
    process,
    parameter: isArray(parameter) ? parameter.join(",") : "",
    startDateTime,
    endDateTime
    // startDateTime: "2025-01-17 00:00:00",
    // endDateTime: "2026-02-10 23:59:59",
    // part: "Cimarron BP 4D Top",
    // process: "M8-6",
    // parameter: ""
  };
  initParam.pageNum = 1;

  const subs = (statisticType || []).map((item: any) => {
    return { prop: item.toLocaleLowerCase(), label: item };
  });
  columns = [..._columns, ...subs];
  // console.log(columns);
  tableKey.value++;
  // proTable.value?.getTableList();
};
onMounted(() => {
  // search({});
});
defineExpose({ search });
</script>
