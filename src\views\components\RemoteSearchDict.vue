<template>
  <div style="width: 100%">
    <el-select
      v-model="selectedLabel"
      filterable
      remote
      reserve-keyword
      :placeholder="$t('请搜索选择')"
      :remote-method="remoteMethod"
      :loading="loading"
      @change="handleChange"
      clearable
    >
      <el-option v-for="item in options" :key="item.id" :label="item.label" :value="item.value">
        {{ item.label }}
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts" name="RemoteSearchDict">
import { ref, watch, onMounted } from "vue";
import { debounce } from "lodash";
import { getStaffList } from "@/api/modules/staff";
import { Staff } from "@/typings/staff";
import { isEmpty } from "@/utils/is";
import { getDictDataList } from "@/api/modules/dict";
import useDictStore from "@/stores/modules/dict";
import { Dict } from "@/typings/dict";

interface Props {
  modelValue: any;
  type: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: ""
});

const emit = defineEmits(["update:modelValue", "change"]);
const selectedLabel = ref<string | null>(null);
const options = ref<Dict.ITypeItem[]>([]);
const loading = ref(false);

const search = async (query: string) => {
  if (query === "") {
    return;
  }
  if (query !== "") {
    loading.value = true;
    try {
      const filter = { name: query, pageSize: 20, pageNum: 1 } as any;
      // if (!isEmpty(props.leader)) {
      //   filter.leader = props.leader;
      // }
      const { data } = await getDictDataList(props.type, { dictData: query });

      options.value = data.map(item => {
        item.label = item.dictLabel;
        item.value = item.dictValue;
        item.type = item.dictType;
        item.id = item.dictValue;
        return item;
      });

      console.log(options.value);
    } catch (error) {
      console.error("Error fetching options:", error);
    } finally {
      loading.value = false;
    }
  } else {
    options.value = [];
  }
};

const remoteMethod = debounce((query: string) => {
  search(query);
}, 500);

const handleChange = (id: string) => {
  if (isEmpty(id)) {
    emit("update:modelValue", "");
    emit("change", {});
  } else {
    const selectedItem = options.value.find(item => item.id === id);
    if (selectedItem) {
      emit("update:modelValue", selectedItem.id);
      emit("change", selectedItem);
    }
  }
};

// 监听外部传入的值变化
watch(
  () => props.modelValue,
  name => {
    // if () {
    //
    //   // options.value = [newOption];
    //   // selectedLabel.value = newOption.id;
    // } else {
    //   selectedLabel.value = null;
    // }
    if (!name) {
      selectedLabel.value = null;
      // selectedLabel.value = newOption.id;
    } else {
      if (options.value.length < 1) {
        selectedLabel.value = name;
        search(name);
      }
    }
  },
  { immediate: true }
);

// 移除 onMounted，因为 watch 的 immediate: true 会立即执行
</script>
