<template>
  <el-dropdown trigger="click">
    <div class="avatar">
      <img :src="avatar" alt="avatar" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="logout">
          <el-icon><SwitchButton /></el-icon>{{ $t("退出登录") }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { LOGIN_URL } from "@/config";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/modules/user";
import { ElMessageBox, ElMessage } from "element-plus";
import { isEmpty } from "@/utils/is";
import defAvatar from "@/assets/images/avatar.gif";
import { formatUrl } from "@/utils";
import { Staff } from "@/typings/staff";
import { logoutApi } from "@/api/modules/login";
import { logoutRy } from "@/api/modules/auth";

const router = useRouter();
const userStore = useUserStore();

const avatar = computed(() => {
  return isEmpty(userStore.userInfo.avatar) ? defAvatar : formatUrl(userStore.userInfo.avatar);
});

// 退出登录
const logout = () => {
  ElMessageBox.confirm("您是否确认退出登录?", "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    // 1.执行退出登录接口
    await logoutApi();
    // await logoutRy();
    // 2.清除 Token
    userStore.setToken("");
    userStore.setUserInfo({} as Staff.Item);
    // 3.重定向到登陆页
    router.replace({ path: LOGIN_URL, query: { logout: 1 } });
    ElMessage.success("退出登录成功！");
  });
};
</script>

<style scoped lang="scss">
.avatar {
  width: 40px;
  height: 40px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 50%;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
