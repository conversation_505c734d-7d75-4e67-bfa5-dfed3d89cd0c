import { API_PREFIX } from "@/api/config/servicePort";
import { ReqPage, ResPage } from "@/api/interface";
import http from "@/api";
import { WarningLimits } from "@/typings/warningLimits";
//列表
export const getWarningLimitsList = (params?: ReqPage) => {
  return http.post<ResPage<WarningLimits.Item[]>>(`${API_PREFIX}/warningLimits/list`, params);
};

export const warningLimitsEdit = (params?: WarningLimits.Item) => {
  return http.post(`${API_PREFIX}/warningLimits/save`, params);
};
export const warningLimitsDelete = (params?: any) => {
  return http.get(`${API_PREFIX}/warningLimits/delete`, params);
};
