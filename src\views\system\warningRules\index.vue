<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" :data-callback="dataCallback">
      <!--      表格 header 按钮-->
      <template #tableHeader>
        <el-button type="primary" @click="handleEdit('新增')">新增</el-button>
      </template>
      <template #operation="{ row }">
        <el-button type="primary" link @click="handleEdit('编辑', row)">编辑</el-button>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button :disabled="!isSelected" type="danger" plain @click="batchDelete(selectedListIds as number[])">
          {{ $t("批量删除") }}
        </el-button>
      </template>
    </ProTable>
    <EditModal ref="editModalRef" />
  </div>
</template>

<script setup lang="tsx" name="DictTable">
import { getPostList, createPost, editPost, deletePost } from "@/api/modules/post";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ReqPage, ResPage } from "@/api/interface";
import EditModal from "./components/EditModal.vue";
import { useDateFormat } from "@vueuse/core";
import { useDict } from "@/hooks/useDict";
import { isArray, isEmpty } from "@/utils/is";
import { ref, reactive, toRefs, nextTick } from "vue";
import { useAuthStore } from "@/stores/modules/auth";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useRoute, useRouter } from "vue-router";
import { Post } from "@/typings/post";
import { Switch } from "@element-plus/icons-vue";
import { alertRulesDel, alertRulesEdit, getAlertRulesList } from "@/api/modules/alertRules";
import { AlertRules } from "@/typings/alertRules";
import { delDictType } from "@/api/modules/dict";

const router = useRouter();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const editModalRef = ref<InstanceType<typeof EditModal> | null>(null);

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<Post.Item & ReqPage>({ pageNum: 1, pageSize: 10 } as Post.Item & ReqPage);

const { common_status } = useDict("common_status");

const loading = ref(true);

// 表格配置项
const columns = reactive<ColumnProps<AlertRules.Item>[]>([
  { type: "selection", fixed: "left", width: 60 },
  { prop: "ruleName", label: "预警规则" },
  { prop: "ruleDescription", label: "规则描述" },
  {
    prop: "emailList",
    label: "邮件列表",
    render: ({ row }) => {
      return row.emailLists?.map((item: string) => (
        <div>
          <el-link href={`mailto:${item}`} type="primary" target="_blank">
            {item}
          </el-link>
        </div>
      ));
    }
  },
  {
    prop: "status",
    label: "状态",
    render: ({ row }) => {
      return <el-switch active-value={1} inactive-value={0} v-model={row.status} before-change={() => statusChange(row)} />;
    }
  },
  {
    prop: "operation",
    label: "操作",
    width: 120,
    fixed: "right"
  }
]);

const dataCallback = (data: ResPage<AlertRules.Item>) => {
  nextTick(() => (loading.value = false));
  return {
    list: data.list?.map(item => {
      item.emailLists = item.emailList.split(",");
      return item;
    }),
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  loading.value = true;
  return getAlertRulesList({ ...params });
};

const handleEdit = (title: string, row: Partial<AlertRules.Item> = {}) => {
  const form = title === "新增" ? {} : { ...row };
  const params = {
    title: title + "规则",
    isView: title === "查看",
    form,
    common_status: common_status.value,
    api: alertRulesEdit,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.id)) Object.assign(params.form, { status: 1 });
  editModalRef.value?.acceptParams(params);
};
const batchDelete = async (id: number | number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(alertRulesDel, { ids }, "删除所选信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
const statusChange = (row: AlertRules.Item) => {
  if (loading.value) return false;
  return new Promise(async resolve => {
    await alertRulesEdit({ ...row, status: row.status == 1 ? 0 : 1 }).then(res => {
      if (res.code === 200) resolve(true);
      proTable.value?.getTableList();
    });
  });
};
</script>
