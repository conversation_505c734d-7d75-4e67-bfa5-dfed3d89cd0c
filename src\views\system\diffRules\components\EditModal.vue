<template>
  <el-dialog v-model="visible" width="900px" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <el-row>
        <el-col :span="18">
          <el-form-item label="规则编号" prop="ruleCode">
            <el-input :disabled="true" v-model="form.ruleCode" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="规则描述">
            <!--            <el-row style="width: 100%">-->
            <!--              <el-col :span="4">-->
            <!--                <el-input-number style="width: 130px" v-model="form.product" controls-position="right" />-->
            <!--              </el-col>-->
            <!--              <el-col :span="6" style="text-align: center">个点远离中心线超过</el-col>-->
            <!--              <el-col :span="5">-->
            <!--                <el-input-number style="width: 130px" v-model="form.product" controls-position="right" />-->
            <!--              </el-col>-->
            <!--              <el-col :span="9"> 倍标准差 </el-col>-->
            <!--            </el-row>-->
            <div class="rule-wrap-item">
              <div class="item" v-for="(item, index) in form.ruleList" :key="index">
                <span v-if="item.type === 'text'">{{ item.text }}</span>
                <el-input-number
                  v-if="item.type === 'value'"
                  style="width: 100px"
                  v-model="item.value"
                  controls-position="right"
                  :min="1"
                />
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态">
            <el-switch :active-value="1" :inactive-value="0" v-model="form.active" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed } from "vue";

import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { Post } from "@/typings/post";
import { useDict } from "@/hooks/useDict";
import { SpcRule } from "@/typings/spcRules";
import { parseRuleToStructure } from "@/utils/util";
const { common_status } = useDict("common_status");
interface IState {
  title: string;
  isView: boolean;
  form: Partial<SpcRule.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  ruleCode: [{ required: true, message: "请填写", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  params.form.ruleList = parseRuleToStructure(params.form.ruleDescription, params.form.paramsConfig);
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const _fm: any = {};
      const _f = JSON.parse(JSON.stringify(form.value));

      _fm.id = _f.id;
      _fm.active = _f.active;
      _fm.ruleCode = _f.ruleCode;
      _fm.ruleName = _f.ruleName;

      _fm.paramsConfig = {};
      _f.ruleList.forEach((item: any) => {
        if (item.type === "value") {
          _fm.paramsConfig[item.text] = item.value;
        }
      });
      _fm.paramsConfig = JSON.stringify(_fm.paramsConfig);
      await state.api!({ ..._fm });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style lang="scss" scoped>
.rule-wrap-item {
  .item {
    display: inline-block;
    padding-right: 10px;
    margin-bottom: 10px;
  }
}
</style>
