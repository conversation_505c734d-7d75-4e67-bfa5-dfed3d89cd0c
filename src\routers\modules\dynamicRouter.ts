import router from "@/routers/index";
import { ADMIN_APP_CODE, APP_CODE, HOME_URL, LOGIN_URL } from "@/config";
import { RouteRecordRaw } from "vue-router";
import { ElNotification } from "element-plus";
import { useUserStore } from "@/stores/modules/user";
import { useAuthStore } from "@/stores/modules/auth";
import useLanguageStore from "@/stores/modules/language";
import { getAppDetail } from "@/api/modules/app";
import { isEmpty, isNumber } from "@/utils/is";
import { isHttp } from "@/utils";

// 引入 views 文件夹下所有 vue 文件
const modules = import.meta.glob("@/views/**/*.vue");

/**
 * @description 初始化动态路由
 */
export const initDynamicRouter = async () => {
  const userStore = useUserStore();
  const authStore = useAuthStore();

  const languageStore = useLanguageStore();

  try {
    // alert(111);
    // 1.获取菜单列表 && 按钮权限列表
    await authStore.getAuthMenuList();
    await authStore.getAuthButtonList();
    // await languageStore.getLanguageCodeList();

    // 2.判断当前用户有没有菜单权限
    // console.log(authStore.authMenuListGet, "authStore.authMenuListGet");
    // debugger;
    if (!authStore.authMenuListGet.length) {
      ElNotification({
        title: "无权限访问",
        message: "当前账号无任何菜单权限，请联系系统管理员！",
        type: "warning",
        duration: 3000
      });
      setTimeout(async () => {
        // const { data } = await getAppDetail({ code: ADMIN_APP_CODE });
        // if (!isEmpty(data)) {
        //   return (window.location.href = data.url + "login" + "?appCode=" + APP_CODE);
        // }
        await userStore.setToken("");
      }, 1500);
      return Promise.reject("No permission");
    }
    const flatRoutePath = authStore.flatMenuListGet
      .map(item => item.path)
      .filter(Boolean)
      .filter(v => {
        return !isHttp(v) && !isNumber(v) && v != "#";
      });
    const redirect = flatRoutePath.includes(HOME_URL) ? HOME_URL : (flatRoutePath[0] ?? "/404");
    router.addRoute({ path: "/", redirect });

    // 3.添加动态路由
    authStore.flatMenuListGet.forEach(item => {
      item.children && delete item.children;
      if (item.component && typeof item.component == "string") {
        item.component = modules["/src/views" + item.component + ".vue"] as any;
      }
      if (item.meta?.isFull) {
        router.addRoute(item as unknown as RouteRecordRaw);
      } else {
        router.addRoute("layout", item as unknown as RouteRecordRaw);
      }

      // console.log(item.component, "tem.componentem.componentem.componen");
    });
  } catch (error) {
    // 当按钮 || 菜单请求出错时，重定向到登陆页
    // alert(11);
    console.log("当按钮 || 菜单请求", error);
    userStore.setToken("");
    router.replace(LOGIN_URL);
    return Promise.reject(error);
  }
};
