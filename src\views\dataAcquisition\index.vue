<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" @click="handleImport">导入</el-button>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </template>
      <template #operation="{ row }">
        <el-button type="primary" link @click="handleEdit('编辑', row)">编辑</el-button>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button type="danger" plain :disabled="!isSelected" @click="batchDelete(selectedListIds as number[])">
          {{ $t("批量删除") }}
        </el-button>
      </template>
    </ProTable>
    <EditModal ref="editModalRef" />
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="tsx" name="DictTable">
import {
  getMeasurementExport,
  getMeasurementExportTmpl,
  getMeasurementImport,
  getMeasurementList,
  measurementDel,
  measurementEdit
} from "@/api/modules/measurement";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ReqPage, ResPage } from "@/api/interface";
import EditModal from "./components/EditModal.vue";
import { useDict } from "@/hooks/useDict";
import { isArray, isEmpty } from "@/utils/is";
import { ref, reactive, toRefs, nextTick } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { Post } from "@/typings/post";

import { downloadFile, downloadFileBlob, downloadFileByName } from "@/utils/download";
import ImportExcel from "@/components/ImportExcel/index.vue";
import { Measurement } from "@/typings/measurement";
import { formatParams } from "@/utils/util";
import DateRange from "@/views/components/DateRange.vue";
import RemoteSearchDict from "@/views/components/RemoteSearchDict.vue";
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const editModalRef = ref<InstanceType<typeof EditModal> | null>(null);
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<Measurement.Item & ReqPage>({ pageNum: 1, pageSize: 10, condition: {} } as Measurement.Item & ReqPage);

// const { spc_parameter, spc_part, spc_process, spc_employee } = useDict(
//   "spc_parameter",
//   "spc_part",
//   "spc_process",
//   "spc_employee"
// );
// 表格配置项
const columns = reactive<ColumnProps<Measurement.Item>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "part",
    label: "产品名称",
    width: 200,
    search: {
      el: "select",
      render: () => {
        return (
          // <el-select v-model={initParam.part} placeholder="请选择" clearable>
          //   {spc_part.value.map(item => (
          //     <el-option key={item.value} label={item.label} value={item.value} />
          //   ))}
          // </el-select>
          // <el-input v-model={initParam.part} />
          <RemoteSearchDict type="spc_part" v-model={initParam.part} />
        );
      }
    }
  },
  {
    prop: "process",
    label: "制程",
    search: {
      el: "select",
      render: () => {
        return (
          // <el-select v-model={initParam.process} placeholder="请选择" clearable>
          //   {spc_process.value.map(item => (
          //     <el-option key={item.value} label={item.label} value={item.value} />
          //   ))}
          // </el-select>
          // <el-input v-model={initParam.process} />
          <RemoteSearchDict type="spc_process" v-model={initParam.process} />
        );
      }
    }
  },
  {
    prop: "parameter",
    label: "参数",
    search: {
      el: "select",
      render: () => {
        return (
          // <el-select v-model={initParam.parameter} placeholder="请选择" clearable>
          //   {spc_parameter.value.map(item => (
          //     <el-option key={item.value} label={item.label} value={item.value} />
          //   ))}
          // </el-select>
          // <el-input v-model={initParam.parameter} />
          <RemoteSearchDict type="spc_parameter" v-model={initParam.parameter} />
        );
      }
    }
  },
  { prop: "subgroupId", label: "子组ID", width: 120 },
  { prop: "sampleSize", label: "样本量", width: 120 },
  { prop: "sampleNo", label: "序号" },
  { prop: "measurementValue", label: "测量值", width: 120 },
  { prop: "usl", label: "下公差", width: 120 },
  { prop: "lsl", label: "上公差", width: 120 },
  {
    prop: "employee",
    label: "操作员",
    width: 120,
    search: {
      el: "select",
      render: () => {
        return (
          // <el-select v-model={initParam.employee} placeholder="请选择" clearable>
          //   {spc_employee.value.map(item => (
          //     <el-option key={item.value} label={item.label} value={item.value} />
          //   ))}
          // </el-select>
          <el-input v-model={initParam.employee} />
        );
      }
    }
  },
  { prop: "date", label: "日期", width: 120 },
  {
    prop: "datetime",
    label: "测量日期",
    width: 200,
    isShow: false,
    search: {
      el: "date-picker",
      render: () => {
        return <DateRange type="datetime" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  { prop: "time", label: "时间", width: 120 },
  { prop: "remark", label: "备注" },
  {
    prop: "operation",
    label: "操作",
    width: 120,
    fixed: "right"
  }
]);

const dataCallback = (data: ResPage<Post.Item>) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};
const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const getTableList = (params: any) => {
  const result = formatParams(initParam, params, filterDate.value);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;

  initParam.condition.startDateTime = result.condition.startDate;
  initParam.condition.endDateTime = result.condition.endDate;

  return getMeasurementList(result);
};
const reset = async () => {
  resetCounter.value++;
  filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  initParam.condition = {};
  nextTick(proTable.value?.getTableList);
};
const handleEdit = (title: string, row: Partial<Post.Item> = {}) => {
  const form = { ...row };
  const params = {
    title: title,
    isView: title === "查看",
    form,
    api: measurementEdit,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.id)) Object.assign(params.form, { status: "0" });
  editModalRef.value?.acceptParams(params);
};
const batchDelete = async (id: number | number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(measurementDel, { ids }, "删除所选信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const handleImport = () => {
  const params = {
    title: "数据采集导入",
    importApi: getMeasurementImport,
    tempApi: getMeasurementExportTmpl,
    tempFun: downloadFileBlob,
    getTableList: proTable.value?.getTableList
  };
  ImportExcelRef.value?.acceptParams(params);
};

/**
 * 导出
 * @param row
 */
const handleExport = async (row: Measurement.Item) => {
  await downloadFileByName(getMeasurementExport, initParam);
};
</script>
