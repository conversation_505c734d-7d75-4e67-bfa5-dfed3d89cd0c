<template>
  <el-dialog v-model="visible" width="500px" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      style="margin-top: 30px"
    >
      <p class="title">颜色配置</p>
      <el-row>
        <el-col :span="12">
          <el-form-item label="背景">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="X轴轴线颜色">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="XY轴文字颜色">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="USL线颜色">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标题文字颜色">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="LSL线颜色">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图内文字颜色" prop="postName">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="UCL线颜色" prop="postName">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Legend颜色" prop="postName">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="LCL线颜色" prop="postName">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Y轴标题颜色" prop="postName">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分割线颜色" prop="postName">
            <div class="color-item">
              <span>{{ form.color }}</span>
              <el-color-picker class="picker" v-model="form.color" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <p class="title">控制限配置</p>
      <div>
        <span>是否将当前控制限保存为自定义控制限&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <el-radio-group v-model="form.color">
          <el-radio :value="3">是</el-radio>
          <el-radio :value="6">否</el-radio>
        </el-radio-group>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="UserDrawer">
import { ElMessage, FormInstance } from "element-plus";

import { ref, reactive, toRefs, computed } from "vue";

import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { Post } from "@/typings/post";
import { useDict } from "@/hooks/useDict";
interface IState {
  title: string;
  isView: boolean;
  form: Partial<Post.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  postName: [{ required: true, message: "请填写", trigger: "blur" }],
  postCode: [{ required: true, message: "请填写", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "图表设置",
  form: {}
});

const { form, title, isView } = toRefs(state);

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({ ...form.value, postSort: 1 });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      // const { data } = await getDictDataList({ dictType: form.value.dictType, status: "1" });
      // useDictStore().setDict(form.value.dictType!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.color-item {
  border: 1px #d8d8d8 solid;
  border-radius: 5px;
  padding: 3px;
  width: 200px;
  ::v-deep(.el-color-picker) {
    float: right;
  }
  span {
    margin-left: 20px;
  }
}
.title {
  font-weight: bold;
  font-size: 16px;
  margin: 20px 0;
}
</style>
