<template>
  <el-dialog v-model="visible" width="65%" :destroy-on-close="true" :title="$t(`${title}字典`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <el-row>
        <el-col :span="20">
          <el-col :span="12">
            <el-form-item :label="$t('字典名称')" prop="name">
              <el-input v-model="form.name" :placeholder="$t('请填写字典名称')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('字典别名')" prop="type">
              <el-input v-model="form.type" :placeholder="$t('请填写字典别名')" clearable />
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :label="$t('字典值')" prop="dictData">
              <div class="table-box">
                <ProTable
                  :columns="columns"
                  :data="form.dictData"
                  :pagination="false"
                  :tool-button="false"
                  ref="proTable"
                  max-height="300"
                >
                  <!-- 表格操作 -->
                  <template #operation="{ $index }">
                    <el-button type="danger" link @click="batchDelete($index)">{{ $t("删除") }}</el-button>
                  </template>
                  <template #footerBtn>
                    <el-button type="primary" :icon="CirclePlus" @click="handleAddRow"> {{ $t("追加") }} </el-button>
                    <el-button type="primary" @click="importData"> {{ $t("导入") }} </el-button>
                  </template>
                </ProTable>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('说明')" prop="remark">
              <el-input v-model="form.remark" :placeholder="$t('请填写字典说明')" clearable />
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :label="$t('状态')" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="({ label, value }, index) of common_status" :key="index" :label="value">{{
                  $t(label)
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
  <ImportModal ref="importModelRef" @import-success="importSuccess" />
</template>

<script setup lang="tsx" name="UserDrawer">
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { ElMessage, FormInstance } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { CirclePlus } from "@element-plus/icons-vue";
import { ref, reactive, toRefs, computed } from "vue";
import { Dict } from "@/typings/dict";
import { useDict } from "@/hooks/useDict";
import useDictStore from "@/stores/modules/dict";
import { getDictDataList } from "@/api/modules/dict";
import ImportModal from "./ImportModal.vue";
import { getImportTemplate } from "@/api/modules/dict";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";

interface IState {
  title: string;
  isView: boolean;
  form: Partial<Dict.Detail>;
  common_status: Dict.IDataItem[];
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  name: [{ required: true, message: "请填写字典名称", trigger: "blur" }],
  type: [{ required: true, message: "请填写字典别名", trigger: "blur" }],
  label: [{ required: true, message: "请填写字典键名", trigger: "blur" }],
  value: [{ required: true, message: "请填写字典键值", trigger: "blur" }]
};

const { isZh } = useLanguageCode();

const labelWidth = computed(() => (isZh.value ? "110px" : "150px"));

const { t } = useI18n();

const proTable = ref<ProTableInstance>();

const importModelRef = ref<InstanceType<typeof ImportModal>>();

const { tag_types } = useDict("tag_types");

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  common_status: [],
  form: {}
});

const { form, title, isView, common_status } = toRefs(state);

// 表格配置项
const columns = reactive<ColumnProps<Dict.IDataItem>[]>([
  {
    prop: "label",
    label: "键名",
    render: ({ row, $index }) => {
      return (
        <el-form-item style={{ padding: "15px 0" }} prop={`dictData[${$index}].label`} rules={rules.label}>
          <el-input v-model={row.label} placeholder={t("请输入键名")} />
        </el-form-item>
      );
    }
  },
  {
    prop: "value",
    label: "键值",
    render: ({ row, $index }) => {
      return (
        <el-form-item style={{ padding: "15px 0" }} prop={`dictData[${$index}].value`} rules={rules.value}>
          <el-input v-model={row.value} placeholder={t("请输入键值")} />
        </el-form-item>
      );
    }
  },
  {
    prop: "labelTranslate",
    label: "键名翻译",
    render: ({ row, $index }) => {
      return <span>{t(row.label ?? "")}</span>;
    }
  },
  // {
  //   prop: "sort",
  //   label: "排序",
  //   render: ({ row }) => {
  //     return (
  //       <el-form-item style={{ padding: "15px 0" }}>
  //         <el-input v-model={row.sort} type={"number"} placeholder={"请输入排序"} />
  //       </el-form-item>
  //     );
  //   }
  // },
  // {
  //   prop: "tagType",
  //   label: "标签类型",
  //   render: ({ row }) => {
  //     return (
  //       <el-select v-model={row.tagType}>
  //         {tag_types.value.map(({ label, value }) => (
  //           <el-option label={label} value={value} key={value} />
  //         ))}
  //       </el-select>
  //     );
  //   }
  // },
  // {
  //   prop: "createdTime",
  //   label: "创建时间",
  //   render: ({ row }) => <span>{useDateFormat(row.createdTime, "YYYY-MM-DD HH:mm:ss").value}</span>
  // },
  {
    prop: "status",
    label: "状态",
    enum: common_status,
    fieldNames: { label: "userLabel", value: "userStatus" },
    render: scope => {
      return <el-switch v-model={scope.row.status} active-value={"1"} inactive-value={"0"} />;
    }
  },
  { prop: "operation", label: "操作", width: 100, fixed: "right" }
]);

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

const importSuccess = (data: Dict.IDataItem[]) => {
  const _data = form.value.dictData || [];
  form.value.dictData = [..._data, ...data];
};

const handleAddRow = () => {
  const dictData = form.value.dictData || [];
  console.log("dictData", dictData);
  dictData.push({ status: "1" } as Dict.IDataItem);
  form.value.dictData = dictData;
};

const importData = () => {
  const params = {
    title: "字典",
    tempApi: getImportTemplate,
    getTableList: proTable.value?.getTableList
  };
  importModelRef.value?.acceptParams(params);
};

const batchDelete = (index: number) => {
  console.log(index);
  form.value.dictData = form.value.dictData!.filter((v, i) => i !== index);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!(form.value);
      ElMessage.success({ message: t(`${title.value}字典成功！`) });
      const { data } = await getDictDataList({ type: form.value.type, status: "1" });
      useDictStore().setDict(form.value.type!, data);
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
