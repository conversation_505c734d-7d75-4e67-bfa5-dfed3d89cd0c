<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" @click="handleEdit('新增')">新增项目</el-button>
        <el-button type="primary" @click="handleControls">自定义控制限</el-button>
        <el-button type="primary" @click="handleWarn">自定义预警限</el-button>
        <el-button type="primary" @click="handleImport">导入</el-button>
      </template>
      <template #operation="{ row }">
        <el-button type="primary" link @click="handleEdit('编辑', row)">编辑</el-button>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button :disabled="!isSelected" type="danger" plain @click="batchDelete(selectedListIds as number[])">
          {{ $t("批量删除") }}
        </el-button>
      </template>
    </ProTable>
    <EditModal ref="editModalRef" />
    <SetControls ref="setControlsRef" />
    <SetWarn ref="setWarnRef" />
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="tsx" name="DictTable">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ReqPage, ResPage } from "@/api/interface";
import EditModal from "./components/EditModal.vue";
import SetControls from "./components/SetControls.vue";
import SetWarn from "./components/SetWarn.vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import RemoteSearchDict from "@/views/components/RemoteSearchDict.vue";

import { useDict } from "@/hooks/useDict";
import { isArray, isEmpty } from "@/utils/is";
import { ref, reactive, toRefs, nextTick } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useRoute, useRouter } from "vue-router";
import { downloadFileBlob, downloadTemplateFie } from "@/utils/download";
import { InspectionItems } from "@/typings/inspectionItems";
import {
  getInspectionItemsList,
  inspectionItemsDel,
  inspectionItemsEdit,
  inspectionItemsExportTemplate,
  inspectionItemsImport
} from "@/api/modules/inspectionItems";
import { formatParams } from "@/utils/util";

const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>();

const router = useRouter();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const editModalRef = ref<InstanceType<typeof EditModal> | null>(null);
const setControlsRef = ref<InstanceType<typeof SetControls> | null>(null);
const setWarnRef = ref<InstanceType<typeof SetWarn> | null>(null);
// 如果表格需要初始化请求参数，直接定义传给 Pr oTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<InspectionItems.Item & ReqPage>({ pageNum: 1, pageSize: 10, condition: {} } as InspectionItems.Item &
  ReqPage);

const { spc_parameter, common_status, spc_part, spc_process } = useDict(
  "spc_parameter",
  "common_status",
  "spc_part",
  "spc_process"
);
// 表格配置项
const columns = reactive<ColumnProps<InspectionItems.Item>[]>([
  { type: "selection", fixed: "left", width: 60 },
  {
    prop: "part",
    label: "产品名称",
    search: {
      el: "select",
      render: () => {
        return (
          // <el-select v-model={initParam.part} placeholder="请选择" clearable>
          //   {spc_part.value.map(item => (
          //     <el-option key={item.value} label={item.label} value={item.value} />
          //   ))}
          // </el-select>
          // <el-input v-model={initParam.part} />
          <RemoteSearchDict type="spc_part" v-model={initParam.part} />
        );
      }
    }
  },
  {
    prop: "process",
    label: "制程",
    search: {
      el: "select",
      render: () => {
        return (
          // <el-select v-model={initParam.process} placeholder="请选择" clearable>
          //   {spc_parameter.value.map(item => (
          //     <el-option key={item.value} label={item.label} value={item.value} />
          //   ))}
          // </el-select>
          // <el-input v-model={initParam.process} />
          <RemoteSearchDict type="spc_process" v-model={initParam.process} />
        );
      }
    }
  },
  {
    prop: "parameter",
    label: "参数",
    search: {
      el: "select",
      render: () => {
        return (
          // <el-select v-model={initParam.parameter} placeholder="请选择" clearable>
          //   {spc_parameter.value.map(item => (
          //     <el-option key={item.value} label={item.label} value={item.value} />
          //   ))}
          // </el-select>
          // <el-input v-model={initParam.parameter} />
          <RemoteSearchDict type="spc_parameter" v-model={initParam.parameter} />
        );
      }
    }
  },
  { prop: "dataType", label: "数据类型" },
  { prop: "attributes", label: "属性" },
  { prop: "formulaVariables", label: "关联参数" },
  { prop: "calculationFormula", label: "计算公式" },
  { prop: "lsl", label: "LSL" },
  { prop: "usl", label: "USL" },
  { prop: "chartType", label: "控制图" },
  { prop: "subgroupSize", label: "子组大小" },
  { prop: "validationRules", label: "判异规则" },
  { prop: "warningRules", label: "预警规则" },
  {
    prop: "displayInDashboard",
    label: "首页展示",
    render: ({ row }) => {
      return row.displayInDashboard == 1 ? "是" : "否";
    }
  },
  {
    prop: "operation",
    label: "操作",
    width: 100,
    fixed: "right"
  }
]);

const dataCallback = (data: ResPage<InspectionItems.Item>) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: any) => {
  const result = formatParams(initParam, params);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  return getInspectionItemsList(result);
};
const reset = async () => {
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  nextTick(proTable.value?.getTableList);
};
const batchDelete = async (id: number | number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(inspectionItemsDel, { ids }, "删除所选信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const handleEdit = (title: string, row: Partial<InspectionItems.Item> = {}) => {
  const form = title === "新增" ? {} : { ...row };
  const params = {
    title,
    isView: title === "查看",
    form,
    api: inspectionItemsEdit,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.id)) Object.assign(params.form, { dataType: "连续数据" });
  editModalRef.value?.acceptParams(params);
};

const handleControls = (row: InspectionItems.Item) => {
  check();
  const form = { ...currentRow.value };
  const params = {
    title: "自定义控制限",
    isView: false,
    form,
    api: inspectionItemsEdit,
    getTableList: proTable.value?.getTableList
  };

  setControlsRef.value?.acceptParams(params);
};

const handleWarn = () => {
  check();
  const form = { ...currentRow.value };
  const params = {
    title: "自定义预警限",
    isView: false,
    form,
    api: null,
    getTableList: proTable.value?.getTableList
  };

  setWarnRef.value?.acceptParams(params);
};

const handleImport = () => {
  const params = {
    title: "检测项管理导入",
    importApi: inspectionItemsImport,
    tempApi: inspectionItemsExportTemplate,
    tempFun: downloadFileBlob,
    getTableList: proTable.value?.getTableList
  };
  ImportExcelRef.value?.acceptParams(params);
};
</script>
