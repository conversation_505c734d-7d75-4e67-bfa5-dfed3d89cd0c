import { createRouter, createWebHashHistory, createWebHistory } from "vue-router";
import { staticRouter, errorRouter } from "@/routers/modules/staticRouter";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import { ADMIN_APP_CODE, APP_CODE, LOGIN_URL, ROUTER_WHITE_LIST } from "@/config";
import { useUserStore } from "@/stores/modules/user";
import { useAuthStore } from "@/stores/modules/auth";
import { useUrlSearchParams } from "@vueuse/core";
import NProgress from "@/config/nprogress";
import { isEmpty } from "@/utils/is";
import { localGet, getMessage } from "@/utils";
import { getAppDetail } from "@/api/modules/app";

const mode = import.meta.env.VITE_ROUTER_MODE;

const routerMode = {
  hash: () => createWebHashHistory(),
  history: () => createWebHistory()
};

/**
 * @description 📚 路由参数配置简介
 * @param path ==> 路由菜单访问路径
 * @param name ==> 路由 name (对应页面组件 name, 可用作 KeepAlive 缓存标识 && 按钮权限筛选)
 * @param redirect ==> 路由重定向地址
 * @param component ==> 视图文件路径
 * @param meta ==> 路由菜单元信息
 * @param meta.icon ==> 菜单和面包屑对应的图标
 * @param meta.title ==> 路由标题 (用作 document.title || 菜单的名称)
 * @param meta.activeMenu ==> 当前路由为详情页时，需要高亮的菜单
 * @param meta.isLink ==> 路由外链时填写的访问地址
 * @param meta.isHide ==> 是否在菜单中隐藏 (通常列表详情页需要隐藏)
 * @param meta.isFull ==> 菜单是否全屏 (示例：数据大屏页面)
 * @param meta.isAffix ==> 菜单是否固定在标签页中 (首页通常是固定项)
 * @param meta.isKeepAlive ==> 当前路由是否缓存
 * */
const router = createRouter({
  history: routerMode[mode](),
  routes: [...staticRouter, ...errorRouter],
  strict: false,
  scrollBehavior: () => ({ left: 0, top: 0 })
});

/**
 * @description 路由拦截 beforeEach
 * */
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();
  const authStore = useAuthStore();
  const {
    token: key = "",
    authKey = "",
    redirect
  } = useUrlSearchParams<{ token: string; authKey: string; refresh: string; redirect: string }>("hash");

  if (to.query.token) {
    await useUserStore().setToken(to.query.token);
  }

  // 1.NProgress 开始
  NProgress.start();

  // 2.动态设置标题
  const title = import.meta.env.VITE_GLOB_APP_TITLE;
  document.title = to.meta.title ? `${getMessage(`${to.meta.title}`)} - ${getMessage(title)}` : getMessage(title);
  if (!isEmpty(authKey)) {
    userStore.setToken(decodeURIComponent(authKey));
    const updatedHash = window.location.hash
      .replace(/authKey=[^&]*&?/, "")
      .replace(/&$/, "")
      .replace(/redirect=[^&]*&?/, "")
      .replace(/&$/, "");
    window.location.hash = updatedHash;
    if (!isEmpty(redirect)) {
      // alert(111);
      // alert(111);
      window.location.href = decodeURIComponent(redirect);
    }
    return window.location.reload();
  }

  // 3.判断是访问登陆页，有 Token 就在当前页面，没有 Token 重置路由到登陆页
  // if (to.path.toLocaleLowerCase() !== LOGIN_URL) {
  // console.log(to.path.toLocaleLowerCase(), LOGIN_URL);
  // const { data } = await getAppDetail({ code: ADMIN_APP_CODE });
  // if (!isEmpty(data)) {
  //   if (!isEmpty(to.query.logout)) {
  //     return (window.location.href = data.url + "?appCode=" + APP_CODE + "&logout=" + to.query.logout);
  //   }
  // debugger;
  // return (window.location.href = LOGIN_URL + "?appCode=" + APP_CODE + "&redirect=" + encodeURIComponent(window.location.href));
  // }
  // if (userStore.token) return next(from.fullPath);
  // resetRouter();
  // return next();
  // }
  // console.log(authStore.authMenuListGet, "34234224");
  if (to.path.toLocaleLowerCase() === LOGIN_URL) {
    console.log("到login");
    if (userStore.token && isEmpty(APP_CODE)) return next(from.fullPath);
    resetRouter();
    // console.log("to", to); /**/
    return next();
  }

  // 4.判断访问页面是否在路由白名单地址(静态路由)中，如果存在直接放行
  if (ROUTER_WHITE_LIST.includes(to.path)) {
    return next();
  }

  // 5.判断是否有 Token，没有重定向到 login 页面
  // if (!userStore.token) {
  //   return next({ path: LOGIN_URL, replace: true });
  // }
  //
  // 6.如果没有菜单列表，就重新请求菜单列表并添加动态路由
  if (!authStore.authMenuListGet.length) {
    await initDynamicRouter();
    return next({ ...to, replace: true });
  } else {
    next();
  }
  //
  // // 7.存储 routerName 做按钮权限筛选
  // await authStore.setRouteName(to.name as string);
  //
  // // 8.正常访问页面
  // next();
});

/**
 * @description 重置路由
 * */
export const resetRouter = () => {
  const authStore = useAuthStore();
  authStore.flatMenuListGet.forEach(route => {
    const { name } = route;
    if (name && router.hasRoute(name)) router.removeRoute(name);
  });
};

/**
 * @description 路由跳转错误
 * */
router.onError(error => {
  NProgress.done();
  console.warn("路由错误", error.message);
});

/**
 * @description 路由跳转结束
 * */
router.afterEach(() => {
  NProgress.done();
});

export default router;
