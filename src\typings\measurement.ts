export namespace Measurement {
  export interface Item {
    /**
     * 测量时间
     */
    date: string;
    /**
     * 数据ID
     */
    detailId: number;
    /**
     * 操作员
     */
    employee: string;
    /**
     * 下规格限
     */
    lsl: number;
    /**
     * 测量值
     */
    measurementValue: number;
    /**
     * 参数名称
     */
    parameter: string;
    /**
     * 产品名称
     */
    part: string;
    /**
     * 制程名称
     */
    process: string;
    /**
     * 备注
     */
    remark: null;
    /**
     * 样本序号
     */
    sampleNo: number;
    /**
     * 样本量
     */
    sampleSize: number;
    /**
     * 子组ID
     */
    subgroupId: number;
    time: string;
    /**
     * 上规格限
     */
    usl: number;
    earliestTime: string;
    latestTime: string;
  }
}
