<template>
  <el-dialog v-model="dialogVisible" :title="$t(`批量添加${parameter.title}`)" :destroy-on-close="true" width="580px" draggable>
    <el-form class="drawer-multiColumn-form" label-width="100px">
      <el-form-item :label="$t('模板下载')">
        <el-button type="primary" :icon="Download" @click="downloadTemp"> {{ $t("点击下载") }}</el-button>
      </el-form-item>
      <el-form-item :label="$t('文件上传')">
        <el-upload
          action="#"
          class="upload"
          :drag="true"
          :limit="excelLimit"
          :multiple="true"
          :show-file-list="true"
          :http-request="uploadExcel"
          :before-upload="beforeExcelUpload"
          :on-exceed="handleExceed"
          :on-error="excelUploadError"
          :accept="parameter.fileType!.join(',')"
        >
          <slot name="empty">
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">{{ $t("将文件拖到此处，或点击上传") }}</div>
          </slot>
          <template #tip>
            <slot name="tip">
              <div class="el-upload__tip">{{ $t("请上传 .xls , .xlsx 标准格式文件，文件最大为") }} {{ parameter.fileSize }}M</div>
            </slot>
          </template>
        </el-upload>
      </el-form-item>
      <!-- <el-form-item label="数据覆盖 :">
        <el-switch v-model="isCover" />
      </el-form-item> -->
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts" name="ImportExcel">
import { ref } from "vue";
import { useDownload } from "@/hooks/useDownload";
import { Download } from "@element-plus/icons-vue";
import { ElNotification, UploadRequestOptions, UploadRawFile } from "element-plus";
import * as XLSX from "xlsx"; // vue3可用此引入

import { Dict } from "@/typings/dict";
import { useI18n } from "vue-i18n";

export interface ExcelParameterProps {
  title: string; // 标题
  fileSize?: number; // 上传文件的大小
  fileType?: File.ExcelMimeType[]; // 上传文件的类型
  tempApi?: (params: any) => Promise<any>; // 下载模板的Api
  importApi?: (params: any) => Promise<any>; // 批量导入的Api
  getTableList?: () => void; // 获取表格数据的Api
}

const emits = defineEmits<{ (e: "importSuccess", data: Dict.IDataItem[]): void }>();

// 是否覆盖数据
const isCover = ref(false);
// 最大文件上传数
const excelLimit = ref(1);
// dialog状态
const dialogVisible = ref(false);
// 父组件传过来的参数
const parameter = ref<ExcelParameterProps>({
  title: "",
  fileSize: 5,
  fileType: ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]
});

const { t } = useI18n();

// 接收父组件参数
const acceptParams = (params: ExcelParameterProps) => {
  parameter.value = { ...parameter.value, ...params };
  dialogVisible.value = true;
};

// Excel 导入模板下载
const downloadTemp = () => {
  if (!parameter.value.tempApi) return;
  useDownload(parameter.value.tempApi, `${parameter.value.title}模板`);
};

// 文件上传
const uploadExcel = async (param: UploadRequestOptions) => {
  let excelFormData = new FormData();
  excelFormData.append("file", param.file);
  excelFormData.append("isCover", isCover.value as unknown as Blob);

  const fileReader = new FileReader();
  fileReader.onload = ev => {
    const workbook = XLSX.read(ev.target?.result, {
      type: "binary"
    });
    const wsname = workbook.SheetNames[0];
    const ws = XLSX.utils.sheet_to_json(workbook.Sheets[wsname]);
    const data = formatData(ws);
    emits("importSuccess", data);
    excelUploadSuccess();
  };
  fileReader.onerror = ev => excelUploadError();
  fileReader.readAsBinaryString(param.file);
  dialogVisible.value = false;
};

const formatData = (data: any[]): Dict.IDataItem[] => {
  const result = data.reduce((prev, curr) => {
    return [...prev, { label: curr["键名"], value: curr["键值"], status: "1" }];
  }, [] as Dict.IDataItem[]);
  return result;
};

/**
 * @description 文件上传之前判断
 * @param file 上传的文件
 * */
const beforeExcelUpload = (file: UploadRawFile) => {
  const isExcel = parameter.value.fileType!.includes(file.type as File.ExcelMimeType);
  const fileSize = file.size / 1024 / 1024 < parameter.value.fileSize!;
  if (!isExcel)
    ElNotification({
      title: t("温馨提示"),
      message: t("上传文件只能是 xls / xlsx 格式！"),
      type: "warning"
    });
  if (!fileSize)
    setTimeout(() => {
      ElNotification({
        title: t("温馨提示"),
        message: `${t("上传文件大小不能超过")} ${parameter.value.fileSize}MB！`,
        type: "warning"
      });
    }, 0);
  return isExcel && fileSize;
};

// 文件数超出提示
const handleExceed = () => {
  ElNotification({
    title: t("温馨提示"),
    message: t("最多只能上传一个文件！"),
    type: "warning"
  });
};

// 上传错误提示
const excelUploadError = () => {
  ElNotification({
    title: t("温馨提示"),
    message: t(`批量添加${parameter.value.title}失败，请您重新上传！`),
    type: "error"
  });
};

// 上传成功提示
const excelUploadSuccess = () => {
  ElNotification({
    title: t("温馨提示"),
    message: t(`批量添加${parameter.value.title}成功！`),
    type: "success"
  });
};

defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.upload {
  width: 80%;
}
</style>
