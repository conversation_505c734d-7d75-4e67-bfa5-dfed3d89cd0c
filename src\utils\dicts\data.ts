import { StatusTypes } from "./type";

/**
 * 操作状态
 */
export const operationStatus: StatusTypes[] = [
  {
    label: "停用",
    status: 1,
    value: 1,
    tagType: "success"
  },
  {
    label: "正常",
    status: 0,
    value: 0,
    tagType: "danger"
  }
];
export const common_yes_no: StatusTypes[] = [
  {
    label: "是",
    value: 1
  },
  {
    label: "否",
    value: 0
  }
];
export const common_status: StatusTypes[] = [
  {
    label: "停用",
    status: 1,
    value: 1,
    tagType: "success"
  },
  {
    label: "正常",
    status: 0,
    value: 0,
    tagType: "danger"
  }
];

export const job_grade: StatusTypes[] = [
  {
    label: "是",
    value: 1
  },
  {
    label: "否",
    value: 0
  }
];
export const audit_status: StatusTypes[] = [
  {
    label: "是",
    value: 1
  },
  {
    label: "否",
    value: 0
  }
];
export const date_types: StatusTypes[] = [
  {
    label: "静态时间",
    value: 1
  },
  {
    label: "动态时间",
    value: 2
  }
];

export const chartTypes = {
  R: "极差图",
  X: "均值图"
};
