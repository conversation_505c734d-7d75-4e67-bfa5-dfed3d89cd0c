<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @reset="reset"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'dict:add'" type="primary" @click="openDictModal('新增')">{{ $t("新增") }}</el-button>
        <el-button v-auth="'dict:edit'" type="primary" @click="openDictModal('编辑')">{{ $t("编辑") }}</el-button>
      </template>
      <template #dictType="{ row }">
        <el-link type="primary" @click="handleView(row)">{{ row.dictType }}</el-link>
      </template>
      <template #footerBtn="{ selectedListIds, isSelected }">
        <el-button
          v-auth="'dict:delete'"
          :disabled="!isSelected"
          type="danger"
          plain
          @click="batchDelete(selectedListIds as number[])"
        >
          {{ $t("批量删除") }}
        </el-button>
      </template>
    </ProTable>
    <DictModal ref="dictModalRef" />
  </div>
</template>

<script setup lang="tsx" name="DictTable">
import { createDict, delDictType, editDict, getDictTypeList } from "@/api/modules/dict";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ReqPage, ResPage } from "@/api/interface";
import DictModal from "./components/DictModal.vue";
import { useDateFormat } from "@vueuse/core";
import { useDict } from "@/hooks/useDict";
import { Dict } from "@/typings/dict";
import { isArray, isEmpty } from "@/utils/is";
import { ref, reactive, toRefs, nextTick } from "vue";
import { useAuthStore } from "@/stores/modules/auth";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useRouter } from "vue-router";
import { DimensionsQuery } from "@/typings/productionData";
import { formatParams } from "@/utils/util";

const router = useRouter();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const dictModalRef = ref<InstanceType<typeof DictModal> | null>(null);

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<Dict.ITypeItem & ReqPage>({ pageNum: 1, pageSize: 10 } as Dict.ITypeItem & ReqPage);

const { common_status } = useDict("common_status");

// 表格配置项
const columns = reactive<ColumnProps<Dict.ITypeItem>[]>([
  { type: "selection", fixed: "left", width: 60 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "dictName",
    label: "字典名称",
    search: {
      el: "input",
      render: () => {
        return <el-input v-model={initParam.dictName} />;
      }
    }
  },
  {
    prop: "dictType",
    label: "字典别名",
    search: {
      el: "input",
      render: () => {
        return <el-input v-model={initParam.dictType} />;
      }
    }
  },
  { prop: "remark", label: "说明" },
  {
    prop: "status",
    label: "状态",
    tag: false,
    enum: common_status,
    search: { el: "select" },
    fieldNames: { label: "label", value: "value" }
  },
  {
    prop: "createdTime",
    label: "创建时间",
    render: ({ row }) => <span>{useDateFormat(row.createdTime, "YYYY-MM-DD HH:mm:ss").value}</span>
  }
  // ...(visibleOperationCol(auth.authButtonList, pageButtons)
  //   ? [
  //       {
  //         prop: "operation",
  //         label: "操作",
  //         width: getOperationColWidth(auth.authButtonList, pageButtons),
  //         fixed: "right"
  //       }
  //     ]
  //   : [])
]);
let queryParams = reactive<Dict.ITypeItem>({} as Dict.ITypeItem);

const dataCallback = (data: ResPage<Dict.ITypeItem>) => {
  return {
    list: data.list?.map(item => {
      item.id = item.dictId;
      return item;
    }),
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

const getTableList = (params: Dict.ITypeItem & ReqPage) => {
  const result = formatParams(initParam, params);
  initParam.pageNum = result.pageNum;
  initParam.pageSize = result.pageSize;
  const p = JSON.parse(JSON.stringify({ ...result, ...result.condition }));
  delete p.condition;
  return getDictTypeList(p);
};

const batchDelete = async (id: number | number[]) => {
  const ids = isArray(id) ? id : [id];
  await useHandleData(delDictType, ids.join(","), "删除所选字典信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openDictModal = (title: string, row: Partial<Dict.Detail> = {}) => {
  if (title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    form,
    common_status: common_status.value,
    api: title === "新增" ? createDict : title === "编辑" ? editDict : void 0,
    getTableList: proTable.value?.getTableList
  };
  if (isEmpty(form.id)) Object.assign(params.form, { status: "0" });
  dictModalRef.value?.acceptParams(params);
};
const reset = async () => {
  // resetCounter.value++;
  // filterDate.value = [];
  for (let key in initParam) {
    if (initParam.hasOwnProperty(key)) {
      delete initParam[key];
    }
  }
  initParam.condition = {};
  nextTick(proTable.value?.getTableList);
};
const handleView = (row: Dict.ITypeItem) => {
  router.push({
    path: "/system/dict-data",
    query: {
      dictType: row.dictType
    }
  });
};
</script>
