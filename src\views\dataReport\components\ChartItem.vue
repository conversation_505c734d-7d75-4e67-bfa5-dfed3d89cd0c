<template>
  <div class="chart-card" @click.stop="viewListInfo">
    <div class="chart" ref="chartRef"></div>
    <ul class="rule">
      <li v-for="(value, key) in rules" :key="key">
        <el-text type="warning">{{ key }}:{{ value }}</el-text>
      </li>
    </ul>
    <DataInfoModal ref="dataInfoModalRef" />
    <RemarkModal ref="remarkModalRef" />
    <DataListInfoModal ref="dataListInfoModalRef" />
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, warn, watch } from "vue";
import { chartTypes } from "@/utils/dicts";
import * as echarts from "echarts/core";
import { GridComponent, LegendComponent, TooltipComponent, TitleComponent } from "echarts/components";
import { LineChart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import { EChartsOption } from "echarts";
import RemarkModal from "./RemarkModal.vue";
import DataInfoModal from "./DataInfoModal.vue";
import DataListInfoModal from "./DataListInfoModal.vue";
import { isBoolean } from "@/utils/is";

const dataInfoModalRef = ref();
const remarkModalRef = ref();
const dataListInfoModalRef = ref();
// 按需引入ECharts模块
echarts.use([GridComponent, LegendComponent, TooltipComponent, LineChart, CanvasRenderer, TitleComponent]);

const props: any = defineProps({
  data: {
    type: Object, // 替换为具体接口
    default: () => ({}) // 必须用工厂函数
  },
  rang: {
    type: Array, // 替换为具体接口
    default: () => [0, 48] // 必须用工厂函数
  }
});

const rang = computed(() => {
  return props.rang;
});

watch(
  rang,
  () => {
    console.log(rang);
    setChart();
  },
  { deep: true }
);

const chartRef = ref();
const list: any = ref();

const rules = computed(() => {
  return props.data.rule;
});

const rightClick = computed(() => {
  return isBoolean(props.data.rightClick) ? props.data.rightClick : false;
});

const infoEdit = computed(() => {
  return isBoolean(props.data.infoEdit) ? props.data.infoEdit : false;
});
const listInfoView = computed(() => {
  return isBoolean(props.data.listInfoView) ? props.data.listInfoView : false;
});

let chartObj: any = null;
const setChart = () => {
  if (chartObj != null) {
    chartObj.dispose();
  }

  // 初始化图表
  chartObj = echarts.init(chartRef.value);
  const data: any = props.data;
  const list = data.chartDataList.filter(item => {
    return item.subgroupId >= rang.value[0] && item.subgroupId <= rang.value[1];
  });
  const title: string = data.title ? data.title + chartTypes[data.chartType] : chartTypes[data.chartType] || "";

  const XData = list.map((item: any) => item.subgroupId);
  const cl = list.map((item: any) => item.cl);
  const usl = list.map((item: any) => item.usl);
  const lsl = list.map((item: any) => item.lsl);
  const datas = list.map((item: any) => {
    return { value: item.data, itemStyle: { color: item.outOfControl ? "red" : "#3a7bd5" } };
  });
  const option: EChartsOption = {
    title: {
      text: title,
      left: 0
    },
    tooltip: {
      trigger: "axis",
      formatter: (params: any) => {
        const obj = list[params[0].dataIndex];
        return `子组ID: ${obj.subgroupId}<br/>
                  测量值: ${parseFloat(obj.data.toFixed(5))}<br/>
                  CL: ${parseFloat(obj.cl.toFixed(5))}<br/>
                  USL: ${parseFloat(obj.usl.toFixed(5))}<br/>
                  LSL: ${parseFloat(obj.lsl.toFixed(5))}`;
      }
    },
    legend: {
      data: ["测量值", "CL", "USL", "LSL"],
      right: 10,
      top: 0
    },
    grid: {
      left: "3%",
      right: "30",
      bottom: "15%",
      top: "50",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: XData,
      // name: "子组ID",
      nameLocation: "middle",
      nameGap: 30,
      boundaryGap: false
    },
    yAxis: {
      type: "value",
      name: ""
    },
    series: [
      {
        name: "测量值",
        type: "line",
        data: datas,
        lineStyle: { color: "#3a7bd5", width: 2 },
        symbol: "circle",
        symbolSize: 8,
        smooth: true,
        markPoint: {
          data: [{ type: "max", value: "111" }]
        },
        trigger: "item"
      },
      {
        name: "CL",
        type: "line",
        data: cl,
        lineStyle: { color: "#4caf50", width: 1, type: "solid" },
        symbol: "none",
        step: "middle",
        endLabel: {
          show: true,
          formatter: () => {
            return "CL";
          }
        }
      },
      {
        name: "USL",
        type: "line",
        data: lsl,
        lineStyle: { color: "#f44336", width: 1, type: "dashed" },
        symbol: "none",
        step: "middle",
        endLabel: {
          show: true,
          formatter: () => {
            return "USL";
          }
        }
      },
      {
        name: "LSL",
        type: "line",
        data: usl,
        lineStyle: { color: "#f44336", width: 1, type: "dashed" },
        symbol: "none",
        step: "middle",
        endLabel: {
          show: true,
          formatter: () => {
            return "LSL";
          }
        }
      }
    ]
  };

  chartObj.setOption(option);

  // 绑定点击事件
  if (infoEdit.value) {
    chartObj.on("click", function (params: any) {
      if (params.componentType === "series" && params.seriesName === "测量值") {
        dataInfoModalRef.value.acceptParams({
          subgroupId: params.name,
          chartType: data.chartType,
          part: data.part,
          process: data.process,
          parameter: data.parameter,
          isView: data.isView
        });
      }
    });
  }

  if (rightClick.value) {
    chartObj.on("contextmenu", params => {
      // 检查是否为目标系列（通过系列名或类型判断）
      if (params.seriesName === "测量值" && params.componentType === "series") {
        // 阻止浏览器默认右键菜单
        params.event.event.preventDefault();

        // 获取被点击的数据点信息
        // const pointIndex = params.dataIndex; // 数据索引
        // const pointValue = params.value; // 数据值
        // const seriesName = params.seriesName; // 系列名称
        remarkModalRef.value.acceptParams({
          subgroupId: params.name,
          chartType: data.chartType,
          part: data.part,
          process: data.process,
          parameter: data.parameter,
          isView: data.isView,
          measurementId: params.name
        });
      }
    });
  }

  // if (listInfoView.value) {
  //   chartObj.on("click", function (params: any) {
  //     // if (params.componentType === "series" && params.seriesName === "测量值") {
  //       dataListInfoModalRef.value.acceptParams({
  //         list: data.details
  //         // subgroupId: params.name,
  //         // chartType: data.chartType,
  //         // part: data.part,
  //         // process: data.process,
  //         // parameter: data.parameter,
  //         // isView: data.isView
  //       });
  //     // }
  //   });
  // }
};

const viewListInfo = () => {
  if (!listInfoView.value) return;
  dataListInfoModalRef.value.acceptParams({
    list: props.data.details
    // subgroupId: params.name,
    // chartType: data.chartType,
    // part: data.part,
    // process: data.process,
    // parameter: data.parameter,
    // isView: data.isView
  });
};
onMounted(() => {
  setChart();
});
</script>

<style scoped lang="scss">
.chart-card {
  width: 100%;
}
.chart {
  height: 300px;
  width: 100%;
}
.rule {
  color: yellow;
  line-height: 20px;
  font-size: 12px;
}
</style>
