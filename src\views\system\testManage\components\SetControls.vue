<template>
  <el-dialog v-model="visible" width="70%" :destroy-on-close="true" :title="$t(`${title}`)">
    <el-form
      ref="formRef"
      :label-width="labelWidth"
      :show-message="isZh"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
    >
      <ProTable :columns="columns" :data="list" :pagination="false" :tool-button="false" ref="proTable" max-height="500">
        <!-- 表格操作 -->
        <template #operation="{ row, $index }">
          <el-button type="primary" link v-if="!row.edit" @click="handleEdit($index)">编辑</el-button>
          <el-button type="primary" link v-if="row.edit" @click="handleSave($index)">保存</el-button>
          <el-button type="danger" link @click="handleDel($index)">删除</el-button>
        </template>
        <template #footerBtn>
          <el-button type="primary" :icon="CirclePlus" @click="handleAddRow">添加行</el-button>
        </template>
      </ProTable>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("关 闭") }}</el-button>
      <!--      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>-->
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="SetControls">
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { ControlLimits } from "@/typings/controlLimit";
import { useDict } from "@/hooks/useDict";
import { CirclePlus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { controlLimitsDelete, controlLimitsEdit, getControlLimitsList } from "@/api/modules/controlLimit";
import { useHandleData } from "@/hooks/useHandleData";
const { product_names } = useDict("product_names");
import { formatDate, useDateFormat } from "@vueuse/core";
import { isDate, isEmpty } from "@/utils/is";
interface IState {
  title: string;
  isView: boolean;
  form: Partial<ControlLimits.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const baseItem = {
  edit: true,
  ucl: undefined,
  cl: undefined,
  lcl: undefined,
  effectiveDate: undefined
};

const list = ref<ControlLimits.Item[]>([]);
const rules = {};

const { isZh } = useLanguageCode();

const labelWidth = 0;

const { t } = useI18n();

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {}
});

const { form, title, isView } = toRefs(state);
const paramsQuery: any = ref({
  pageSize: 10,
  pageNum: 1,
  condition: {
    itemId: 1
  }
});

// 表格配置项

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = async (params: IState) => {
  Object.assign(state, params);
  paramsQuery.value.condition.itemId = params.form.id;
  await getList();
  setVisible(true);
};

const getList = async () => {
  const { data } = await getControlLimitsList(paramsQuery.value);
  list.value = data.list;
};
// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!({ ...form.value });
      ElMessage.success({ message: t(`${title.value}成功！`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

const columns = reactive<ColumnProps<ControlLimits.Item>[]>([
  { type: "index", fixed: "left", width: 80, label: "序号" },
  {
    prop: "ucl",
    label: "UCL",
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-form-item style={{ padding: "15px 0" }} prop={`list[${$index}].label`} rules={rules.label}>
          <el-input v-model={row.ucl} placeholder="请输入" />
        </el-form-item>
      ) : (
        row.ucl
      );
    }
  },
  {
    prop: "cl",
    label: "CL",
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-form-item style={{ padding: "15px 0" }} prop={`list[${$index}].label`} rules={rules.label}>
          <el-input v-model={row.cl} placeholder="请输入" />
        </el-form-item>
      ) : (
        row.cl
      );
    }
  },
  {
    prop: "lcl",
    label: "LCL",
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-form-item style={{ padding: "15px 0" }} prop={`list[${$index}].label`} rules={rules.label}>
          <el-input v-model={row.lcl} placeholder="请输入" />
        </el-form-item>
      ) : (
        row.lcl
      );
    }
  },
  {
    prop: "effectiveDate",
    label: "生效时间",
    width: 170,
    render: ({ row, $index }) => {
      return row.edit ? (
        <el-form-item style={{ padding: "15px 0" }} prop={`list[${$index}].label`} rules={rules.label}>
          <el-date-picker v-model={row.effectiveDate} format="YYYY-MM-DD" value-format="YYYY-MM-DD" type="date" />
        </el-form-item>
      ) : (
        row.effectiveDate
      );
    }
  },
  {
    prop: "updateBy",
    label: "修改人"
  },
  {
    prop: "updateTime",
    label: "修改时间",
    width: 180
  },
  { prop: "operation", label: "操作", width: 120, fixed: "right" }
]);

const handleDel = async (index: number) => {
  // const batchDelete = async (id: number | number[]) => {
  //   const ids = isArray(id) ? id : [id];
  await useHandleData(controlLimitsDelete, { ctrlId: list.value[index].ctrlId }, "删除所选信息");

  list.value = list.value!.filter((v, i) => i !== index);
  // proTable.value?.clearSelection();
  // proTable.value?.getTableList();
  // };
};

const handleEdit = (index: number) => {
  if (list.value.find(item => item.edit)) {
    ElMessage.warning({ message: "请保存后再操作" });
  }

  list.value[index]!.edit = true;
};
const handleSave = async (index: number) => {
  const params = { ...list.value[index] };

  if (isEmpty(params.itemId)) {
    params.itemId = paramsQuery.value.condition.itemId;
  }
  // if (!isEmpty(params.effectiveDate)) {
  // params.effectiveDate = dayjs(params.effectiveDate, "YYYY-MM-DD");
  // }
  await controlLimitsEdit(params);
  list.value[index]!.edit = false;
  await getList();
};
const handleAddRow = () => {
  if (list.value.find(item => item.edit)) {
    ElMessage.warning({ message: "请保存后再操作" });
    return;
  }
  const row = JSON.parse(JSON.stringify(baseItem));
  list.value.push(row);
};
defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.top-item {
  display: block;
  width: 100%;
  ::v-deep(.el-form-item__label) {
    display: block;
  }
  ::v-deep(.el-form-item__content) {
    display: block;
  }
}

.btn {
  margin-right: 20px;
  ::v-deep(span) {
    color: var(--el-color-white) !important;
  }
}

::v-deep(.el-form-item) {
  margin-bottom: 0 !important;
  padding: 0 !important;
}
</style>
